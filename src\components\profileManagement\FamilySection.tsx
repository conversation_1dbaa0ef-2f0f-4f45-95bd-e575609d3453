import React from "react";
import { Accordion, Form } from "react-bootstrap";

interface FamilySectionProps {
  formik: any;
  handleBlurUncontrolled: (field: string) => (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const FamilySection: React.FC<FamilySectionProps> = ({ formik, handleBlurUncontrolled }) => (
  <Accordion.Item eventKey="5">
    <Accordion.Header>Family</Accordion.Header>
    <Accordion.Body>
      <div className="form-input-group d-flex flex-wrap">
        <Form.Group className="form-input">
          <Form.Label>Kids</Form.Label>
          <Form.Control
            type="number"
            placeholder="Enter Kids"
            name="kids"
            defaultValue={formik.values.kids}
            onBlur={handleBlurUncontrolled("kids")}
          />
        </Form.Group>
      </div>
    </Accordion.Body>
  </Accordion.Item>
);

export default FamilySection; 