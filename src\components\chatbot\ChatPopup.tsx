import { useState, useEffect, useRef } from 'react';
import './styles.scss';
import useChatStore from '@/stores/useChatStore';
import { Button, Form, InputGroup, Card, CloseButton, Image } from 'react-bootstrap';
import { IMAGE_PATHS } from '@/utils/image-path';
import { AttachSquare, Send, Gift, Happyemoji } from 'iconsax-react';

const ChatPopup = () => {
  const { activeChatUser, setActiveChatUser, messages, sendMessage } = useChatStore();
  const [input, setInput] = useState('');
  const messagesEndRef = useRef(null);

  // Dummy messages data
  const dummyMessages = {
    [activeChatUser?.id || 'default']: [
      { sender: 'me', text: 'Hey, how are you?' },
      { sender: 'other', text: 'Doing great, thanks for asking!' },
      { sender: 'me', text: 'Cool, any plans for the weekend?' },
      { sender: 'other', text: 'Just chilling, maybe a hike. You?' },
      { sender: 'me', text: 'Nice! I might go to a concert.' },
      { sender: 'other', text: 'That sounds fun! What band?' },
    ],
  };

  // Scroll to the bottom of the chat when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, activeChatUser]);

  if (!activeChatUser) return null;

  const handleSend = () => {
    if (input.trim()) {
      sendMessage(activeChatUser.id, input);
      setInput('');
    }
  };

  return (
    <Card className="position-fixed chatpopup">
      <div className="d-flex justify-content-between align-items-center chatpopup-header">
        <div className='d-flex gap-2 align-items-center'>
          <Image src={IMAGE_PATHS.UserProfile} alt="" className='user-img' />
          <div className='d-flex flex-column gap-1'>
            <h6 className='name mb-0'>{activeChatUser.name}</h6>
            <p className='location mb-0'>Bridlepath, Canada</p>
          </div>
        </div>
        <CloseButton variant="black" className='close-btn' onClick={() => setActiveChatUser(null)} />
      </div>
      <div className="message-screen" style={{ flex: 1 }}>
        {(dummyMessages[activeChatUser.id] || []).map((msg, idx) => (
          <div
            key={idx}
            className={`message ${msg.sender === 'me' ? 'message-send ms-auto text-end' : 'message-receive text-start me-auto'}`}
          >
            {msg.text}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      <div className="chatpopup-footer">
        <Form onSubmit={(e) => { e.preventDefault(); handleSend(); }}>
          <InputGroup className='gap-3'>
            <Form.Control
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Enter Message"
            />
            <div className='d-flex align-items-center gap-3'> 
              <Button className='icons-btn'>
                <AttachSquare size="24" color="#000000" />
              </Button>
              <Button className='icons-btn'><Gift size="24" color="#000000" variant="Outline" /></Button>
              <Button className='icons-btn'><Happyemoji size="24" color="#000000" variant="Outline" /></Button>
              <Button variant="primary" className='send-btn' onClick={handleSend}>
                <Send size="24" color="#ffffff" variant="Bold" />
              </Button>
            </div>

          </InputGroup>
        </Form>
      </div>
    </Card>
  );
};

export default ChatPopup;