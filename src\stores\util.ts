import { ConfirmModalConfig } from "@/types";
import { create } from "zustand";

interface PayloadInterface {
  confirmModalConfig: ConfirmModalConfig;
}

export const DEFAULT_PRESET = "custom1";

const initialState = {
  confirmModalConfig: {
    visible: false,
    data: {},
  },
};

const useUtilStore = create((set: any) => ({
  ...initialState,
  setConfirmModalConfig: (data: ConfirmModalConfig) =>
    set((state: PayloadInterface) => ({
      ...state,
      confirmModalConfig: data,
    })),
})
);

export const {
  setConfirmModalConfig,
} = useUtilStore.getState();

export default useUtilStore;
