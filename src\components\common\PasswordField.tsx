import { FormikErrors, FormikTouched, FormikValues } from "formik";
import { Eye, EyeSlash } from "iconsax-react";
import React, { useState } from "react";
import { Form } from "react-bootstrap";

interface PasswordFieldProps {
  label: string;
  name: string;
  placeholder: string;
  values: FormikValues;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleBlur: (e: React.FocusEvent<HTMLInputElement>) => void;
  touched: FormikTouched<FormikValues>;
  errors: FormikErrors<FormikValues>;
  submitCount?: number;
}

const PasswordField: React.FC<PasswordFieldProps> = ({
  label,
  name,
  placeholder,
  values,
  handleChange,
  handleBlur,
  touched,
  errors,
  submitCount = 0,
}) => {
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const toggleVisibility = () => setShowPassword((prev) => !prev);

  const showError = (!!touched[name] || submitCount > 0) && !!errors[name];

  return (
    <Form.Group className="form-input">
      <Form.Label>{label}</Form.Label>
      <div className="position-relative">
        <Form.Control
          name={name}
          type={showPassword ? "text" : "password"}
          placeholder={placeholder}
          value={values[name]}
          onChange={handleChange}
          onBlur={handleBlur}
          isInvalid={showError}
        />
        {showPassword ? (
          <EyeSlash
            size={24}
            color="#141414"
            className="position-absolute end-0 top-50 translate-middle-y me-2"
            style={{ cursor: "pointer" }}
            onClick={toggleVisibility}
          />
        ) : (
          <Eye
            size={24}
            color="#141414"
            className="position-absolute end-0 top-50 translate-middle-y me-2"
            style={{ cursor: "pointer" }}
            onClick={toggleVisibility}
          />
        )}
      </div>
      {showError && (
        <Form.Control.Feedback type="invalid" className="d-block text-danger">
          {errors[name]}
        </Form.Control.Feedback>
      )}
    </Form.Group>
  );
};

export default PasswordField;
