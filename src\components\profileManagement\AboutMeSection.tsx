import React from "react";
import { Accordion, Form } from "react-bootstrap";

interface AboutMeSectionProps {
  formik: any;
  handleBlurUncontrolled: (field: string) => (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const AboutMeSection: React.FC<AboutMeSectionProps> = ({ formik, handleBlurUncontrolled }) => (
  <Accordion.Item eventKey="6">
    <Accordion.Header>About Me</Accordion.Header>
    <Accordion.Body>
      <div className="form-input-group d-flex flex-wrap">
        <Form.Group className="form-input w-100">
          <Form.Label>About Me</Form.Label>
          <Form.Control
            as={"textarea"}
            rows={4}
            type="text"
            placeholder="Enter About Me"
            name="aboutMe"
            defaultValue={formik.values.aboutMe}
            onBlur={handleBlurUncontrolled("aboutMe")}
          />
        </Form.Group>
      </div>
    </Accordion.Body>
  </Accordion.Item>
);

export default AboutMeSection; 