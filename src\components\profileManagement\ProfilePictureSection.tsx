import { useUpdateAvatarMutation } from "@/api";
import {
  useGetS3PresignedUrlMutation,
  useUploadFileToS3Mutation,
} from "@/api/utils.api";
import defaultProfile from "@/assets/images/user.png";
import { setUserAvatar } from "@/stores";
import useUserStore from "@/stores/user";
import {
  ACCEPTED_IMAGE_TYPES,
  validateImageFile,
} from "@/utils/imageValidation";
import { Camera } from "iconsax-react";
import React, { useRef, useState } from "react";
import { Form, Image, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";

const ProfilePictureSection = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  const userData = useUserStore((state) => state.userInfo.user);

  const { mutateAsync: getPresignedUrl } = useGetS3PresignedUrlMutation();
  const { mutateAsync: uploadFileToS3 } = useUploadFileToS3Mutation();
  const { mutateAsync: updateAvatar } = useUpdateAvatarMutation();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const error = validateImageFile(file);
    if (error) {
      toast.error(error);
      return;
    }
    setIsUploading(true);
    try {
      const ext = file.name.split(".").pop()?.toLowerCase() || "jpg";
      const presignedRes: any = await getPresignedUrl({
        location: "users",
        type: ext,
        count: 1,
      });
      const fileData = presignedRes?.data?.files?.[0];
      if (!fileData) {
        toast.error("Failed to get S3 upload URL.");
        return;
      }
      await uploadFileToS3({
        url: fileData.url,
        file,
      });

      const result: any = await updateAvatar({
        imagePath: fileData.filename,
      });
      if (result?.success) {
        setUserAvatar(fileData.filename);
      }
    } catch (err: any) {
      console.log(err);
    } finally {
      setIsUploading(false);
    }
  };

  const previewUrl = userData.avatar
    ? import.meta.env.VITE_S3_BASE_URL + userData.avatar
    : defaultProfile;

  return (
    <div className="profile-box">
      <h5 className="fw-bold mb-0">Profile Picture</h5>
      <Form.Group className="profile-upload">
        <div className={`circle position-relative rounded-circle`}>
          <Image
            src={previewUrl}
            className="profile-pic w-100 h-100 rounded-circle object-fit-cover"
            alt="Profile Preview"
          />
          {isUploading && (
            <div
              className="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
              style={{ background: "rgba(0,0,0,0.4)", borderRadius: "50%" }}
            >
              <Spinner animation="border" variant="light" />
            </div>
          )}
          <div className="p-image rounded-circle d-flex align-items-center justify-content-center">
            <Camera size="20" color="#fff" variant="Bold" />
            <Form.Control
              type="file"
              accept={ACCEPTED_IMAGE_TYPES.join(",")}
              className="file-upload"
              onChange={handleFileChange}
              ref={fileInputRef}
            />
          </div>
        </div>
      </Form.Group>
      <p className="mb-0">
        NOTE: Profile pictures will be manually screened by the admin for
        approval.
      </p>
    </div>
  );
};

export default ProfilePictureSection;
