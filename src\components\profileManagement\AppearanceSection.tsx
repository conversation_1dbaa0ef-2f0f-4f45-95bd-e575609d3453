import React from "react";
import { Accordion, Form } from "react-bootstrap";
import MemoizedSelect from "./MemoizedSelect";

interface AppearanceSectionProps {
  formik: any;
  handleBlurUncontrolled: (field: string) => (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (field: string) => (option: any) => void;
  appearanceOptions: Array<{ value: any; label: string }>;
  hairColorOptions: Array<{ value: any; label: string }>;
  eyeColorOptions: Array<{ value: any; label: string }>;
  bodyTypeOptions: Array<{ value: any; label: string }>;
  bestFeatureOptions: Array<{ value: any; label: string }>;
  bodyArtOptions: Array<{ value: any; label: string }>;
  isMasterLoading: boolean;
}

const AppearanceSection: React.FC<AppearanceSectionProps> = ({
  formik,
  handleBlurUncontrolled,
  handleSelectChange,
  appearanceOptions,
  hairColorOptions,
  eyeColorOptions,
  bodyTypeOptions,
  bestFeatureOptions,
  bodyArtOptions,
  isMasterLoading,
}) => (
  <Accordion.Item eventKey="1">
    <Accordion.Header>Appearance</Accordion.Header>
    <Accordion.Body>
      <div className="form-input-group d-flex flex-wrap">
        <Form.Group className="form-input">
          <Form.Label>Appearance</Form.Label>
          <MemoizedSelect
            isSearchable
            options={appearanceOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Appearance"}
            classNamePrefix="select"
            name="appearance"
            value={formik.values.appearance}
            onChange={handleSelectChange("appearance")}
          />
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Hair color</Form.Label>
          <MemoizedSelect
            isSearchable
            options={hairColorOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Hair color"}
            classNamePrefix="select"
            name="hairColor"
            value={formik.values.hairColor}
            onChange={handleSelectChange("hairColor")}
          />
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Eye color</Form.Label>
          <MemoizedSelect
            isSearchable
            options={eyeColorOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Eye color"}
            classNamePrefix="select"
            name="eyeColor"
            value={formik.values.eyeColor}
            onChange={handleSelectChange("eyeColor")}
          />
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Body Type</Form.Label>
          <MemoizedSelect
            isSearchable
            options={bodyTypeOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Body Type"}
            classNamePrefix="select"
            name="bodyType"
            value={formik.values.bodyType}
            onChange={handleSelectChange("bodyType")}
          />
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Best Feature</Form.Label>
          <MemoizedSelect
            isSearchable
            options={bestFeatureOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Best Feature"}
            classNamePrefix="select"
            name="bestFeature"
            value={formik.values.bestFeature}
            onChange={handleSelectChange("bestFeature")}
          />
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Body Art</Form.Label>
          <MemoizedSelect
            isSearchable
            options={bodyArtOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Body Art"}
            classNamePrefix="select"
            name="bodyArt"
            value={formik.values.bodyArt}
            onChange={handleSelectChange("bodyArt")}
          />
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Height (CM)</Form.Label>
          <Form.Control
            type="number"
            placeholder="Enter Height (CM)"
            name="height"
            defaultValue={formik.values.height}
            onBlur={handleBlurUncontrolled("height")}
          />
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Weight (KG)</Form.Label>
          <Form.Control
            type="number"
            placeholder="Enter Weight (KG)"
            name="weight"
            defaultValue={formik.values.weight}
            onBlur={handleBlurUncontrolled("weight")}
          />
        </Form.Group>
      </div>
    </Accordion.Body>
  </Accordion.Item>
);

export default AppearanceSection; 