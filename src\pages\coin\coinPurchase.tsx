import React, { useState } from 'react'
import './styles.scss'
import { Container, Row } from 'react-bootstrap'
import PlanCard from '@/components/coincard/planCard';
const plans = [
  {
    title: 'Basic',
    coins: 300,
    originalPrice: 49,
    currentPrice: 29,

  },
  {
    title: 'Standard',
    coins: 300,
    originalPrice: 49,
    currentPrice: 29,

  },
  {
    title: 'Premium',
    coins: 300,
    originalPrice: 49,
    currentPrice: 29,

  },
  {
    title: 'Ultimate',
    coins: 300,
    originalPrice: 49,
    currentPrice: 29,

  },
];
const coinPurchase: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(0);
  return (
    <div className='coin-purchage-section inner-height'>
      <Container>
        <div className='d-flex flex-column gap-2 coin-purchage-section-content text-center'>
          <h2 className='mb-0 title'>Order Your Discounted Coins Now!</h2>
          <p className='mb-0 description'>14 days unlimited free trial. No contract or credit card required. 14 days unlimited free trial. No contract or credit card required.</p>
        </div>
        <Row>
          {plans.map((plan, index) => (
            <PlanCard
              key={index}
              index={index}
              title={plan.title}
              coins={plan.coins}
              originalPrice={plan.originalPrice}
              currentPrice={plan.currentPrice}
              isActive={activeIndex === index}
            />
          ))}
        </Row>

      </Container>
    </div>
  )
}

export default coinPurchase