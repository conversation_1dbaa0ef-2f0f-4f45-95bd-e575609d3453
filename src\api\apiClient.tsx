import { setConfirmModalConfig } from "@/stores";
import { getAuthToken, resetUserState } from "@/stores/user";
import { getAppOriginURL } from "@/utils";
import axios, { AxiosResponse, InternalAxiosRequestConfig } from "axios";
import toast from "react-hot-toast";

export const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
});

const authRequestInterceptor = (config: InternalAxiosRequestConfig) => {
  const token = getAuthToken();
  if (config.headers) {
    config.headers.Accept = "application/json";
    config.headers['accept-language'] = "en";
  }
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
};

apiClient.interceptors.request.use(authRequestInterceptor);

apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    if (!response?.data?.success) {
      if (response?.data?.message) {
        toast.error(response.data.message, {
          id: "generic-error",
        });
      }
    }
    return response.data;
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      handle401(error);
      return;
    }
    if (typeof error.response === "undefined") {
      toast.error(
        "A network error occurred. Please try again after some time.",
        {
          id: "network-error",
        }
      );
      return;
    }
    toast.error(
      error?.response?.data?.message || "An error occurred, please try again.",
      {
        id: "generic-error",
        duration: 10000,
      }
    );
    return Promise.reject(error);
  }
);

const handle401 = (error: any) => {
  const onSubmit = () => {
    resetUserState();
    window.location.href = getAppOriginURL();
  };
  setConfirmModalConfig({
    visible: true,
    data: {
      onSubmit: () => onSubmit(),
      iconColor: "#B936AD",
      content: {
        heading: "Session Expired",
        description:
          error?.response?.data?.message ||
          "Your session has timed out. Please log in again to continue.",
      },
      showCloseIcon: false,
      buttonText: "Log in again",
    },
  });
};
