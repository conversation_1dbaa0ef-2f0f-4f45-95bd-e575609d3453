import { useState } from "react";

export function useProfileAvatar(initialImage: string) {
  const [preview, setPreview] = useState<string>(initialImage);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith("image/")) {
      const imageUrl = URL.createObjectURL(file);
      setPreview(imageUrl);
    }
  };

  return { preview, handleImageChange, setPreview };
} 