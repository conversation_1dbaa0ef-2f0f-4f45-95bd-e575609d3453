import React from "react";
import { Accordion, Form } from "react-bootstrap";
import MemoizedSelect from "./MemoizedSelect";

interface LifestyleSectionProps {
  formik: any;
  handleSelectChange: (field: string) => (option: any) => void;
  relationshipStatusOptions: Array<{ value: any; label: string }>;
  starSignOptions: Array<{ value: any; label: string }>;
  smokingHabitOptions: Array<{ value: any; label: string }>;
  drinkingHabitOptions: Array<{ value: any; label: string }>;
  sexualOrientationOptions: Array<{ value: any; label: string }>;
  isMasterLoading: boolean;
}

const LifestyleSection: React.FC<LifestyleSectionProps> = ({
  formik,
  handleSelectChange,
  relationshipStatusOptions,
  // starSignOptions,
  smokingHabitOptions,
  drinkingHabitOptions,
  sexualOrientationOptions,
  isMasterLoading,
}) => (
  <Accordion.Item eventKey="2">
    <Accordion.Header>Lifestyle</Accordion.Header>
    <Accordion.Body>
      <div className="form-input-group d-flex flex-wrap">
        <Form.Group className="form-input">
          <Form.Label>Relationship Status</Form.Label>
          <MemoizedSelect
            isSearchable
            options={relationshipStatusOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Relationship Status"}
            classNamePrefix="select"
            name="relationshipStatus"
            value={formik.values.relationshipStatus}
            onChange={handleSelectChange("relationshipStatus")}
          />
        </Form.Group>
        {/* <Form.Group className="form-input">
          <Form.Label>Star Sign</Form.Label>
          <MemoizedSelect
            isSearchable
            options={starSignOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Star Sign"}
            classNamePrefix="select"
            name="starSign"
            value={formik.values.starSign}
            onChange={handleSelectChange("starSign")}
          />
        </Form.Group> */}
        <Form.Group className="form-input">
          <Form.Label>Smoking Habit</Form.Label>
          <MemoizedSelect
            isSearchable
            options={smokingHabitOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Smoking Habit"}
            classNamePrefix="select"
            name="smokingHabit"
            value={formik.values.smokingHabit}
            onChange={handleSelectChange("smokingHabit")}
          />
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Drinking Habit</Form.Label>
          <MemoizedSelect
            isSearchable
            options={drinkingHabitOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Drinking Habit"}
            classNamePrefix="select"
            name="drinkingHabit"
            value={formik.values.drinkingHabit}
            onChange={handleSelectChange("drinkingHabit")}
          />
        </Form.Group>
        <Form.Group className="form-input">
          <Form.Label>Sexual Orientation</Form.Label>
          <MemoizedSelect
            isSearchable
            options={sexualOrientationOptions}
            placeholder={isMasterLoading ? "Loading..." : "Select Sexual Orientation"}
            classNamePrefix="select"
            name="sexualOrientation"
            value={formik.values.sexualOrientation}
            onChange={handleSelectChange("sexualOrientation")}
          />
        </Form.Group>
      </div>
    </Accordion.Body>
  </Accordion.Item>
);

export default LifestyleSection; 