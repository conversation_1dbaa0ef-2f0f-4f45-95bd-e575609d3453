import react from '@vitejs/plugin-react-swc';
import path from 'path';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(() => {
  const env = loadEnv("env", process.cwd());
  return {
    plugins: [react()],
    css: {
      devSourcemap: true,
    },
    server: {
      port: 3000,
      open: true,
      watch: {
        usePolling: true,
      },
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    build: {
      outDir: "./build",
    },
    optimizeDeps: {
      include: ['iconsax-react'],
    },
    base: env.VITE_BASE_URL,
  }
})
