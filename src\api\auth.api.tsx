import { API_ENDPOINTS } from "@/globals";
import {
  ChangePasswordInterface,
  LoginInterface,
  ResetPasswordInterface,
  SignupInterface
} from "@/types";
import { useMutation } from "@tanstack/react-query";
import { apiClient } from "./apiClient";

export const useSignupMutation = () =>
  useMutation({
    mutationFn: async (payload: SignupInterface) => {
      const response = await apiClient.post(API_ENDPOINTS.SIGNUP, payload);
      return response;
    },
  });

export const useVerifyOTPMutation = () =>
  useMutation({
    mutationFn: async (payload: {
      type: string | undefined;
      email: string | undefined;
      otp: string;
    }) => {
      const response = await apiClient.post(API_ENDPOINTS.VERIFY_OTP, payload);
      return response;
    },
  });

export const useResetPasswordMutation = () =>
  useMutation({
    mutationFn: async (payload: ResetPasswordInterface) => {
      const response = await apiClient.post(
        API_ENDPOINTS.RESET_PASSWORD,
        payload
      );
      return response;
    },
  });

export const useChangePasswordMutation = () =>
  useMutation({
    mutationFn: async (
      payload: Omit<ChangePasswordInterface, "confirmPassword">
    ) => {
      const response = await apiClient.put(
        API_ENDPOINTS.CHANGE_PASSWORD,
        payload
      );
      return response;
    },
  });

export const useLoginMutation = () =>
  useMutation({
    mutationFn: async (payload: LoginInterface) => {
      const response = await apiClient.post(API_ENDPOINTS.LOGIN, payload);
      return response;
    },
  });

export const useRequestOTPMutation = () =>
  useMutation({
    mutationFn: async (payload: {
      type: string | undefined;
      email: string | undefined;
      username?: string;
    }) => {
      const response = await apiClient.post(API_ENDPOINTS.REQUEST_OTP, payload);
      return response;
    },
  });

export const useLogoutMutation = () =>
  useMutation({
    mutationFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.LOGOUT);
      return response;
    },
  });
