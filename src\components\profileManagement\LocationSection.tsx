import React from "react";
import { Accordion, Form } from "react-bootstrap";

interface LocationSectionProps {
  formik: any;
  handleBlurUncontrolled: (field: string) => (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const LocationSection: React.FC<LocationSectionProps> = ({ formik, handleBlurUncontrolled }) => (
  <Accordion.Item eventKey="4">
    <Accordion.Header>Location</Accordion.Header>
    <Accordion.Body>
      <div className="form-input-group d-flex flex-wrap">
        <Form.Group className="form-input">
          <Form.Label>City</Form.Label>
          <Form.Control
            type="text"
            placeholder="Enter City"
            name="city"
            defaultValue={formik.values.city}
            onBlur={handleBlurUncontrolled("city")}
          />
        </Form.Group>
      </div>
    </Accordion.Body>
  </Accordion.Item>
);

export default LocationSection; 