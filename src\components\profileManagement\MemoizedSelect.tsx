import React from "react";
import ReactSelect from "react-select";

interface MemoizedSelectProps {
  options: Array<{ value: any; label: string }>;
  value: any;
  onChange: (option: any) => void;
  name?: string;
  isSearchable?: boolean;
  placeholder?: string;
  classNamePrefix?: string;
}

const MemoizedSelect: React.FC<MemoizedSelectProps> = React.memo(({ options, value, onChange, ...props }) => (
  <ReactSelect
    options={options}
    value={options.find((o: { value: any }) => o.value === value) || null}
    onChange={onChange}
    {...props}
  />
));

export default MemoizedSelect; 