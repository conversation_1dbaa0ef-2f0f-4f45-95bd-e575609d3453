@use '@/variables' as *;

.advance-filter-offcanvas {
    .offcanvas-header {
        padding: 24px;
        border-bottom: 1px solid $light-gray-color;

        .offcanvas-title {
            color: $black-color;
            font-size: 20px;
            font-weight: 700;
            line-height: 1.3;
        }

        .btn-close {
            opacity: 1;
        }
    }

    .offcanvas-body {
        padding: 24px;

        .accordion {
            &-item {
                border: none;
                border-bottom: 1px solid $light-gray-color;
                margin-bottom: 1rem;
                border-radius: 0px;

                .accordion-button {
                    background-color: transparent;
                    color: $black-color;
                    font-size: 15px;
                    font-weight: 600;
                    line-height: normal;
                    box-shadow: none;
                    padding: 0px;
                    padding-bottom: 16px;

                    &::after {
                        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M11.9999 10.8284L7.0502 15.7782L5.63599 14.364L11.9999 8L18.3639 14.364L16.9497 15.7782L11.9999 10.8284Z'%3E%3C/path%3E%3C/svg%3E");
                        width: 20px;
                        height: 20px;
                    }
                }

                .accordion-body {
                    padding: 0px;
                    padding-bottom: 16px;

                    .show-more-link {
                        color: $primary-color;
                        font-size: 14px;
                        font-weight: 600;
                        line-height: normal;
                    }

                    .hair-color-selector {
                        .hair-radio {

                            .hair-color-box {
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                                cursor: pointer;
                                gap: 4px;

                                .color-outer-ring {
                                    padding: 2px;
                                    border-radius: 50%;
                                    border: 2px solid;
                                    display: inline-flex;
                                    align-items: center;
                                    justify-content: center;
                                    width: 40px;
                                    height: 40px;
                                }

                                .color-inner-circle {
                                    width: 34px;
                                    height: 34px;
                                    min-width: 34px;
                                    border-radius: 50%;
                                }

                                .color-label {
                                    color: $black-color;
                                    font-size: 10px;
                                    font-weight: 400;
                                    line-height: normal;
                                }
                            }

                            input[type='radio']:checked + .hair-color-box .color-outer-ring {
                                box-shadow: 0 0 0 1px $black-color;

                            }
                        }
                    }

                }
            }
        }


    }

    .filter-btns {
        border-top: 1px solid $light-gray-color;
        background: $white-color;
        box-shadow: 0px -4px 24px 0px rgba(0, 0, 0, 0.15);
        padding: 24px;

        .first-btn {

            border-radius: 8px;
            border: 1px solid $light-gray-color;
            background: $white-color;
            padding: 15px 40px;
            color: $black-color;
            font-size: 14px;
            min-width: auto;

            &:hover {
                background: #79259C;
                color: $white-color;
            }
        }

        .second-btn {
            padding: 15px 40px;
            font-size: 14px;
            min-width: auto;
        }
    }
}