export const getAppOriginURL = () => {
    return `${window.location.origin}${import.meta.env.VITE_BASE_URL?.length > 1 ? import.meta.env.VITE_BASE_URL : ""}`;
};

export const calculateAgeByDOB = (dob: string) => {
    if (!dob) return null;

    const birthDate = new Date(dob);
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();

    const monthDiff = today.getMonth() - birthDate.getMonth();
    const dayDiff = today.getDate() - birthDate.getDate();

    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
        age--;
    }

    return age;
};