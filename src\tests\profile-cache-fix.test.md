# Profile Cache Fix Test

## Issue Description
The profile data wasn't being properly updated after a successful submission and page refresh. The issue occurred because:

1. User updates profile (e.g., kids: 0 → kids: 1) and submits successfully
2. Profile gets updated on the server
3. User refreshes the page
4. useMyProfile query refetches the updated data from the server
5. But <PERSON><PERSON>'s initialValues were still using old cached data because there was no cache invalidation

## Root Cause
- **Missing React Query cache invalidation** after successful profile updates
- **Race condition** between React Query data and Zustand store updates
- **No proper cache management** for profile mutations

## Solution Applied

### 1. Added Cache Invalidation to Mutations
```typescript
// src/api/user.api.ts
export const useUpdateProfileMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (payload: Record<string, any>) => {
      return apiClient.put(API_ENDPOINTS.UPDATE_PROFILE, payload);
    },
    onSuccess: () => {
      // Invalidate and refetch the profile data
      queryClient.invalidateQueries({ queryKey: ["my-profile"] });
    },
  });
};
```

### 2. Updated Data Flow in Components
```typescript
// Use fresh profile data from React Query instead of stale Zustand store
const profileData = myProfile || userData;

const initialValues = {
  // ... use profileData instead of userData
  kids: (profileData as any)?.customer_profile?.kids || undefined,
  // ... other fields
};
```

### 3. Synchronized Store Updates
```typescript
React.useEffect(() => {
  if (myProfile && Object.keys(myProfile).length > 0) {
    setUserInfo({
      ...userInfo,
      user: myProfile,
    });
  }
}, [myProfile, userInfo]);
```

## Test Steps to Verify Fix

1. **Update Profile Data**
   - Go to Profile Management or My Profile
   - Change kids from 0 to 1 (or any other field)
   - Submit the form
   - Verify success message

2. **Refresh Page**
   - Refresh the browser page
   - The form should now show the updated value (kids: 1)
   - Previously it would show the old value (kids: 0)

3. **Verify Cache Invalidation**
   - Open browser dev tools → Network tab
   - After form submission, you should see the profile API call being made
   - The response should contain the updated data
   - The form should reflect the updated data immediately

## Files Modified
- `src/api/user.api.ts` - Added cache invalidation to mutations
- `src/pages/myProfile/index.tsx` - Updated data flow and store sync
- `src/pages/profileManagement/index.tsx` - Updated data flow and store sync

## Expected Behavior After Fix
- Profile updates are immediately reflected in the UI
- Page refresh shows the correct updated values
- No more stale data issues
- Consistent data flow between React Query cache and Zustand store
