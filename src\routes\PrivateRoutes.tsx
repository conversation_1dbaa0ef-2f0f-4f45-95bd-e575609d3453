import Header from "@/components/common/Header";
import { CoinPurchase, Favorites, Flirts, Home, ViewedMe } from "@/pages";
import Messages from "@/pages/messages";
import MyProfile from "@/pages/myProfile";
import ProfileManagement from "@/pages/profileManagement";
import { Route } from "react-router-dom";
import { ROUTE_PATH } from "./routePath";

const PrivateRoutes = () => {
  return (
    <>
      <Route path={ROUTE_PATH.HOME} element={<Home />} />
      <Route element={<Header />}>
        <Route path={ROUTE_PATH.COINPURCHASE} element={<CoinPurchase />} />
        <Route
          path={ROUTE_PATH.PROFILE_MANAGEMENT}
          element={<ProfileManagement />}
        />
        <Route path={ROUTE_PATH.MYPROFILE} element={<MyProfile />} />
        <Route path={ROUTE_PATH.FAVORITES} element={<Favorites />} />
        <Route path={ROUTE_PATH.VIEWEDME} element={<ViewedMe />} />
        <Route path={ROUTE_PATH.FLIRTS} element={<Flirts />} />
        <Route path={ROUTE_PATH.MESSAGES} element={<Messages />} />
      </Route>
    </>
  );
};

export default PrivateRoutes;
