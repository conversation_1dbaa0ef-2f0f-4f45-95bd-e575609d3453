import { useEffect } from "react";
import { CloseButton } from "react-bootstrap";
import { toast, ToastBar, Toaster, useToasterStore } from "react-hot-toast";

const ToastProvider = () => {
  const { toasts } = useToasterStore();
  const TOAST_LIMIT = 1;

  useEffect(() => {
    toasts
      .filter((t) => t.visible)
      .filter((_, i) => i >= TOAST_LIMIT)
      .forEach((t) => toast.dismiss(t.id));
  }, [toasts]);

  const onClose = (t: any) => {
    toast.dismiss(t?.id);
  };

  return (
    <Toaster
      position="top-center"
      toastOptions={{
        style: {
          maxWidth: 500,
          padding: "12px 40px 12px 12px",
          borderRadius: "12px",
          boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
        },
      }}
    >
      {(t) => (
        <ToastBar toast={t}>
          {({ icon, message }) => (
            <>
              <div
                style={{ display: "flex", alignItems: "center", width: "100%" }}
              >
                {icon}
                <span style={{ marginLeft: "10px", fontWeight: "500" }}>
                  {message}
                </span>
              </div>
              <CloseButton
                onClick={() => onClose(t)}
                style={{
                  position: "absolute",
                  top: "5px",
                  right: "5px",
                  fontSize: "12px",
                }}
              />
            </>
          )}
        </ToastBar>
      )}
    </Toaster>
  );
};

export default ToastProvider;
