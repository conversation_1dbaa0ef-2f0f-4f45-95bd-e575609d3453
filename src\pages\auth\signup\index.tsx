import { useRequestOTPMutation } from "@/api/auth.api";
import { useCountries, useMaster } from "@/api/utils.api";
import PasswordField from "@/components/common/PasswordField";
import { SignupSchemaValidation } from "@/formSchema/schemaValidations";
import { ROUTE_PATH } from "@/routes";
import useAuthStore from "@/stores/auth";
import { SignupInterface } from "@/types";
import { useFormik } from "formik";
import { Button, Form } from "react-bootstrap";
import toast from "react-hot-toast";
import { Link, useNavigate } from "react-router-dom";
import ReactSelect from "react-select";

const Signup = () => {
  const { data: countries = [], isLoading: isCountriesLoading } =
    useCountries();
  const { data: master = {}, isLoading: isMasterLoading } = useMaster();
  const { mutateAsync: requestOtp } = useRequestOTPMutation();
  const setSignupUserInfo = useAuthStore((state) => state.setSignupUserInfo);
  const navigate = useNavigate();

  type Country = { id: number; name: string; code: string };
  type OptionType = { value: number; label: string };

  const seekingForOptions = (master.seeking_for || []).map((item: any) => ({
    value: item.id,
    label: item.title,
  }));

  const genderOptions = (master.gender || []).map((item: any) => ({
    value: item.id,
    label: item.title,
  }));

  const formik = useFormik<
    SignupInterface & {
      confirmPassword: string;
      agreeTerms: boolean;
    }
  >({
    initialValues: {
      seekingFor: undefined,
      gender: undefined,
      username: "",
      email: "",
      dob: "",
      countryId: undefined,
      password: "",
      confirmPassword: "",
      agreeTerms: false,
      city: "NYC",
    },
    validationSchema: SignupSchemaValidation,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const signupPayload = {
          seekingFor: values.seekingFor,
          gender: values.gender, // Add gender to payload
          username: values.username,
          email: values.email,
          dob: values.dob,
          countryId: values.countryId,
          city: values.city,
          password: values.password,
        };

        const result: any = await requestOtp({
          type: "SIGN_UP",
          email: values.email,
          username: values.username,
        });
        if (result?.success) {
          toast.success(result?.message);
          setSignupUserInfo(signupPayload);
          navigate(
            `${ROUTE_PATH.VERIFYACCOUNT}?type=SIGN_UP&email=${values.email}`
          );
        }
      } catch (error) {
        console.error("Signup error:", error);
      } finally {
        setSubmitting(false);
      }
    },
  });

  const {
    handleChange,
    handleBlur,
    handleSubmit,
    values,
    touched,
    errors,
    setFieldValue,
    isSubmitting,
  } = formik;

  return (
    <div className="auth-form bg-white d-flex flex-column justify-content-between">
      <div className="d-flex flex-column gap-4">
        <div className="auth-form-heading text-center">
          <h3>
            Welcome to <span>Redsoft!</span>
          </h3>
        </div>
        <Form noValidate onSubmit={handleSubmit}>
          <div className="form-input-group d-flex flex-column">
            <Form.Group className="form-input">
              <Form.Label>I am seeking for</Form.Label>
              <ReactSelect
                options={seekingForOptions}
                isSearchable={false}
                name="seekingFor"
                value={
                  seekingForOptions.find(
                    (option: any) => option.value === values.seekingFor
                  ) || null
                }
                onChange={(option: any) =>
                  setFieldValue("seekingFor", option?.value || 0)
                }
                placeholder={isMasterLoading ? "Loading..." : "Select"}
                classNamePrefix="select"
              />
              {touched.seekingFor && errors.seekingFor && (
                <small className="text-danger">
                  {errors.seekingFor as string}
                </small>
              )}
            </Form.Group>

            <Form.Group className="form-input">
              <Form.Label>Gender</Form.Label>
              <ReactSelect
                options={genderOptions}
                isSearchable={false}
                name="gender"
                value={
                  genderOptions.find(
                    (option: any) => option.value === values.gender
                  ) || null
                }
                onChange={(option: any) =>
                  setFieldValue("gender", option?.value || 0)
                }
                placeholder={isMasterLoading ? "Loading..." : "Select"}
                classNamePrefix="select"
              />
              {touched.gender && errors.gender && (
                <small className="text-danger">{errors.gender as string}</small>
              )}
            </Form.Group>

            <Form.Group className="form-input">
              <Form.Label>User Name</Form.Label>
              <Form.Control
                name="username"
                type="text"
                placeholder="Please enter username"
                value={values.username}
                onChange={handleChange}
                onBlur={handleBlur}
                isInvalid={!!touched.username && !!errors.username}
              />
              <Form.Control.Feedback type="invalid" className="text-danger">
                {errors.username}
              </Form.Control.Feedback>
            </Form.Group>

            <Form.Group className="form-input">
              <Form.Label>Email</Form.Label>
              <Form.Control
                name="email"
                type="email"
                placeholder="Please enter email"
                value={values.email}
                onChange={handleChange}
                onBlur={handleBlur}
                isInvalid={!!touched.email && !!errors.email}
              />
              <Form.Control.Feedback type="invalid" className="text-danger">
                {errors.email}
              </Form.Control.Feedback>
            </Form.Group>

            <Form.Group className="form-input">
              <Form.Label>Date of Birth</Form.Label>
              <Form.Control
                name="dob"
                type="date"
                placeholder="Please enter Date of Birth"
                value={values.dob}
                onChange={handleChange}
                onBlur={handleBlur}
                isInvalid={!!touched.dob && !!errors.dob}
                max={
                  new Date(
                    new Date().setFullYear(new Date().getFullYear() - 18)
                  )
                    .toISOString()
                    .split("T")[0]
                }
              />
              <Form.Control.Feedback type="invalid" className="text-danger">
                {errors.dob}
              </Form.Control.Feedback>
            </Form.Group>

            <Form.Group className="form-input">
              <Form.Label>Country</Form.Label>
              <ReactSelect
                options={countries.map((c: Country) => ({
                  value: c.id,
                  label: c.name,
                }))}
                isSearchable={true}
                name="countryId"
                value={
                  countries
                    .map((c: Country) => ({ value: c.id, label: c.name }))
                    .find(
                      (option: OptionType) => option.value === values.countryId
                    ) || null
                }
                onChange={(option: OptionType | null) =>
                  setFieldValue("countryId", option?.value || 0)
                }
                placeholder={
                  isCountriesLoading ? "Loading..." : "Select country"
                }
                classNamePrefix="select"
              />
              {touched.countryId && errors.countryId && (
                <small className="text-danger">
                  {errors.countryId as string}
                </small>
              )}
            </Form.Group>

            <PasswordField
              label="Password"
              name="password"
              placeholder="Password (8 or more characters)"
              values={values}
              handleChange={handleChange}
              handleBlur={handleBlur}
              touched={touched}
              errors={errors}
            />

            <PasswordField
              label="Confirm Password"
              name="confirmPassword"
              placeholder="Confirm your password"
              values={values}
              handleChange={handleChange}
              handleBlur={handleBlur}
              touched={touched}
              errors={errors}
            />

            <Form.Group className="form-check mt-2">
              <Form.Check type="checkbox" id={`check-api-checkbox`}>
                <Form.Check.Input
                  type="checkbox"
                  name="agreeTerms"
                  checked={values.agreeTerms}
                  onChange={handleChange}
                  isInvalid={!!touched.agreeTerms && !!errors.agreeTerms}
                />
                <Form.Check.Label>
                  I am over 18. I have read & understand & accept the{" "}
                  <Link to="">Terms & Conditions</Link> and{" "}
                  <Link to="">Privacy & Cookie Policy</Link>. I also agree to
                  receive account updates, newsletters, notifications and
                  communication from other members on email sent by
                  Discretedating.club
                </Form.Check.Label>
              </Form.Check>

              <small className="text-danger">
                {touched.agreeTerms && errors.agreeTerms}
              </small>
            </Form.Group>
          </div>

          <Button
            type="submit"
            className="w-100 mt-3 mt-sm-4"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Registering..." : "Register Now"}
          </Button>
        </Form>
      </div>

      <div className="text-center mt-3 mt-sm-4">
        <p className="mb-0">
          Have an account? <Link to={ROUTE_PATH.LOGIN}>Sign in</Link>
        </p>
      </div>
    </div>
  );
};

export default Signup;
