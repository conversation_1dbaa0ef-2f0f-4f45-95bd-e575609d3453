import React, { useState } from 'react';
import { Offcanvas, Form, Button, Accordion, Row, Col } from 'react-bootstrap';
import './styles.scss'
import { Link } from 'react-router-dom';

interface Props {
    show: boolean;
    onClose: () => void;
}

const hairColors = [
    { label: 'Dark Brown', value: 'dark-brown', color: '#5b0000' },
    { label: 'Light Brown', value: 'light-brown', color: '#8b4c00' },
    { label: 'Black', value: 'black', color: '#000000' },
    { label: 'Grey', value: 'grey', color: '#999999' },
    { label: 'Red', value: 'red', color: '#ff0000' },
    { label: 'Blue', value: 'blue', color: '#298eff' },
    { label: 'Not Given', value: 'not-given', color: '#f8f8f8' },
    { label: 'Other', value: 'other', color: '#eeeeee' },
];

const AdvancedFilterPanel: React.FC<Props> = ({ show, onClose }) => {
    const [selected, setSelected] = useState('');
    return (
        <Offcanvas show={show} onHide={onClose} placement="end" className="advance-filter-offcanvas">
            <Offcanvas.Header closeButton>
                <Offcanvas.Title>Advanced Filters</Offcanvas.Title>
            </Offcanvas.Header>
            <Offcanvas.Body className="d-flex flex-column gap-4">
                <Accordion defaultActiveKey="0">
                    <Accordion.Item eventKey="0">
                        <Accordion.Header>Relationship Status</Accordion.Header>
                        <Accordion.Body>
                            <Form.Group className="form-input pb-3">
                                <div key="radio" className="d-flex gap-2 flex-column">
                                    <Form.Check type="radio" id="check-api-radio1" name="relationshipStatus">
                                        <Form.Check.Input type="radio" name="relationshipStatus" isValid />
                                        <Form.Check.Label>Single</Form.Check.Label>
                                    </Form.Check>
                                    <Form.Check type="radio" id="check-api-radio2" name="relationshipStatus">
                                        <Form.Check.Input type="radio" name="relationshipStatus" isValid />
                                        <Form.Check.Label>Married</Form.Check.Label>
                                    </Form.Check>
                                    <Form.Check type="radio" id="check-api-radio3" name="relationshipStatus">
                                        <Form.Check.Input type="radio" name="relationshipStatus" isValid />
                                        <Form.Check.Label>Divorced</Form.Check.Label>
                                    </Form.Check>
                                </div>
                            </Form.Group>
                            <Link to="" className='show-more-link d-flex align-items-center gap-1'>Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5893 13.0899C10.2638 13.4153 9.73618 13.4153 9.41074 13.0899L5.24408 8.92324C4.91864 8.5978 4.91864 8.07017 5.24408 7.74473C5.56951 7.41929 6.09715 7.41929 6.42259 7.74473L10 11.3221L13.5774 7.74473C13.9028 7.41929 14.4305 7.41929 14.7559 7.74473C15.0814 8.07017 15.0814 8.5978 14.7559 8.92324L10.5893 13.0899Z" fill="#B936AD" />
                            </svg> </Link>

                        </Accordion.Body>
                    </Accordion.Item>
                    <Accordion.Item eventKey="1">
                        <Accordion.Header>Hair Color</Accordion.Header>
                        <Accordion.Body>
                            <div className='d-flex flex-column gap-3'>
                                <Row className="g-3 hair-color-selector">
                                    {hairColors.map((item, idx) => (
                                        <Col xs={3} className="text-center" key={idx}>
                                            <Form.Check type="radio" id={`hair-${item.value}`} className="hair-radio ps-0">
                                                <Form.Check.Input
                                                    type="radio"
                                                    name="hairColor"
                                                    value={item.value}
                                                    checked={selected === item.value}
                                                    onChange={() => setSelected(item.value)}
                                                    className="d-none"
                                                />
                                                <Form.Check.Label className="hair-color-box">
                                                    <span
                                                        className="color-outer-ring"
                                                        style={{ borderColor: item.color }}
                                                    >
                                                        <span
                                                            className="color-inner-circle"
                                                            style={{ backgroundColor: item.color }}
                                                        ></span>
                                                    </span>
                                                    <div className="color-label">{item.label}</div>
                                                </Form.Check.Label>
                                            </Form.Check>
                                        </Col>
                                    ))}
                                </Row>
                                <Link to="" className='show-more-link d-flex align-items-center gap-1'>Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5893 13.0899C10.2638 13.4153 9.73618 13.4153 9.41074 13.0899L5.24408 8.92324C4.91864 8.5978 4.91864 8.07017 5.24408 7.74473C5.56951 7.41929 6.09715 7.41929 6.42259 7.74473L10 11.3221L13.5774 7.74473C13.9028 7.41929 14.4305 7.41929 14.7559 7.74473C15.0814 8.07017 15.0814 8.5978 14.7559 8.92324L10.5893 13.0899Z" fill="#B936AD" />
                                </svg> </Link>
                            </div>

                        </Accordion.Body>
                    </Accordion.Item>
                    <Accordion.Item eventKey="2">
                        <Accordion.Header>Best Features</Accordion.Header>
                        <Accordion.Body>
                            <div className="form-input pb-3 d-flex gap-2 flex-column">
                                <Form.Check type="checkbox" label="My Hair" />
                                <Form.Check type="checkbox" label="My Lips" />
                                <Form.Check type="checkbox" label="My Legs" />
                            </div>
                            <Link to="" className='show-more-link d-flex align-items-center gap-1'>Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5893 13.0899C10.2638 13.4153 9.73618 13.4153 9.41074 13.0899L5.24408 8.92324C4.91864 8.5978 4.91864 8.07017 5.24408 7.74473C5.56951 7.41929 6.09715 7.41929 6.42259 7.74473L10 11.3221L13.5774 7.74473C13.9028 7.41929 14.4305 7.41929 14.7559 7.74473C15.0814 8.07017 15.0814 8.5978 14.7559 8.92324L10.5893 13.0899Z" fill="#B936AD" />
                            </svg> </Link>

                        </Accordion.Body>
                    </Accordion.Item>
                    <Accordion.Item eventKey="3">
                        <Accordion.Header>Eye Color</Accordion.Header>
                        <Accordion.Body>
                            <div className='form-input pb-3 d-flex gap-2 flex-column'>
                                <Form.Check type="checkbox" label="Brown" />
                                <Form.Check type="checkbox" label="Blue" />
                                <Form.Check type="checkbox" label="Green" />
                            </div>
                            <Link to="" className='show-more-link d-flex align-items-center gap-1'>Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5893 13.0899C10.2638 13.4153 9.73618 13.4153 9.41074 13.0899L5.24408 8.92324C4.91864 8.5978 4.91864 8.07017 5.24408 7.74473C5.56951 7.41929 6.09715 7.41929 6.42259 7.74473L10 11.3221L13.5774 7.74473C13.9028 7.41929 14.4305 7.41929 14.7559 7.74473C15.0814 8.07017 15.0814 8.5978 14.7559 8.92324L10.5893 13.0899Z" fill="#B936AD" />
                            </svg> </Link>

                        </Accordion.Body>
                    </Accordion.Item>
                    <Accordion.Item eventKey="4">
                        <Accordion.Header>Personality</Accordion.Header>
                        <Accordion.Body>
                            <div className='form-input pb-3 d-flex gap-2 flex-column'>
                                <Form.Check type="checkbox" label="Option1" />
                                <Form.Check type="checkbox" label="Option2" />
                                <Form.Check type="checkbox" label="Option3" />
                            </div>
                            <Link to="" className='show-more-link d-flex align-items-center gap-1'>Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5893 13.0899C10.2638 13.4153 9.73618 13.4153 9.41074 13.0899L5.24408 8.92324C4.91864 8.5978 4.91864 8.07017 5.24408 7.74473C5.56951 7.41929 6.09715 7.41929 6.42259 7.74473L10 11.3221L13.5774 7.74473C13.9028 7.41929 14.4305 7.41929 14.7559 7.74473C15.0814 8.07017 15.0814 8.5978 14.7559 8.92324L10.5893 13.0899Z" fill="#B936AD" />
                            </svg> </Link>

                        </Accordion.Body>
                    </Accordion.Item>
                    <Accordion.Item eventKey="5">
                        <Accordion.Header>Appearance</Accordion.Header>
                        <Accordion.Body>
                            <div className='form-input pb-3 d-flex gap-2 flex-column'>
                                <Form.Check type="checkbox" label="Option1" />
                                <Form.Check type="checkbox" label="Option2" />
                                <Form.Check type="checkbox" label="Option3" />
                            </div>
                            <Link to="" className='show-more-link d-flex align-items-center gap-1'>Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5893 13.0899C10.2638 13.4153 9.73618 13.4153 9.41074 13.0899L5.24408 8.92324C4.91864 8.5978 4.91864 8.07017 5.24408 7.74473C5.56951 7.41929 6.09715 7.41929 6.42259 7.74473L10 11.3221L13.5774 7.74473C13.9028 7.41929 14.4305 7.41929 14.7559 7.74473C15.0814 8.07017 15.0814 8.5978 14.7559 8.92324L10.5893 13.0899Z" fill="#B936AD" />
                            </svg> </Link>

                        </Accordion.Body>
                    </Accordion.Item>
                    <Accordion.Item eventKey="6">
                        <Accordion.Header>Body Type</Accordion.Header>
                        <Accordion.Body>
                            <div className='form-input pb-3 d-flex gap-2 flex-column'>
                                <Form.Check type="checkbox" label="Option1" />
                                <Form.Check type="checkbox" label="Option2" />
                                <Form.Check type="checkbox" label="Option3" />
                            </div>
                            <Link to="" className='show-more-link d-flex align-items-center gap-1'>Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5893 13.0899C10.2638 13.4153 9.73618 13.4153 9.41074 13.0899L5.24408 8.92324C4.91864 8.5978 4.91864 8.07017 5.24408 7.74473C5.56951 7.41929 6.09715 7.41929 6.42259 7.74473L10 11.3221L13.5774 7.74473C13.9028 7.41929 14.4305 7.41929 14.7559 7.74473C15.0814 8.07017 15.0814 8.5978 14.7559 8.92324L10.5893 13.0899Z" fill="#B936AD" />
                            </svg> </Link>

                        </Accordion.Body>
                    </Accordion.Item>
                    <Accordion.Item eventKey="7">
                        <Accordion.Header>Smoking Habits</Accordion.Header>
                        <Accordion.Body>
                            <div className='form-input pb-3 d-flex gap-2 flex-column'>
                                <Form.Check type="checkbox" label="Yes" />
                                <Form.Check type="checkbox" label="No" />
                            </div>
                            <Link to="" className='show-more-link d-flex align-items-center gap-1'>Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5893 13.0899C10.2638 13.4153 9.73618 13.4153 9.41074 13.0899L5.24408 8.92324C4.91864 8.5978 4.91864 8.07017 5.24408 7.74473C5.56951 7.41929 6.09715 7.41929 6.42259 7.74473L10 11.3221L13.5774 7.74473C13.9028 7.41929 14.4305 7.41929 14.7559 7.74473C15.0814 8.07017 15.0814 8.5978 14.7559 8.92324L10.5893 13.0899Z" fill="#B936AD" />
                            </svg> </Link>

                        </Accordion.Body>
                    </Accordion.Item>
                    <Accordion.Item eventKey="8">
                        <Accordion.Header>Drinking Habits</Accordion.Header>
                        <Accordion.Body>
                            <div className='form-input pb-3 d-flex gap-2 flex-column'>
                                <Form.Check type="checkbox" label="Yes" />
                                <Form.Check type="checkbox" label="No" />
                            </div>
                            <Link to="" className='show-more-link d-flex align-items-center gap-1'>Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 20 20" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5893 13.0899C10.2638 13.4153 9.73618 13.4153 9.41074 13.0899L5.24408 8.92324C4.91864 8.5978 4.91864 8.07017 5.24408 7.74473C5.56951 7.41929 6.09715 7.41929 6.42259 7.74473L10 11.3221L13.5774 7.74473C13.9028 7.41929 14.4305 7.41929 14.7559 7.74473C15.0814 8.07017 15.0814 8.5978 14.7559 8.92324L10.5893 13.0899Z" fill="#B936AD" />
                            </svg> </Link>

                        </Accordion.Body>
                    </Accordion.Item>


                </Accordion>

            </Offcanvas.Body>
            <div className="mt-auto d-flex justify-content-between gap-3 filter-btns">
                <Button variant="outline-secondary" className="w-50 first-btn">Reset</Button>
                <Button variant="primary" className="w-50 second-btn">120 results</Button>
            </div>
        </Offcanvas>
    );
};

export default AdvancedFilterPanel;
