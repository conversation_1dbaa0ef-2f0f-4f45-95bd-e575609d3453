import * as Yup from 'yup';
import { confirmPasswordValidation, DOBValidation, passwordValidation } from './common';

export const SignupSchemaValidation = Yup.object().shape({
    seekingFor: Yup.number().required("Please select your preference"),
    gender: Yup.number().required("Please select your gender"),
    username: Yup.string().min(3, "Too short").required("Username is required"),
    email: Yup.string().email("Invalid email").required("Email is required"),
    dob: DOBValidation,
    countryId: Yup.number().required("Country is required"),
    // city: Yup.string().required("City is required"),
    password: passwordValidation("Password"),
    confirmPassword: confirmPasswordValidation("password"),
    agreeTerms: Yup.boolean().oneOf([true], "You must accept the terms"),
});
