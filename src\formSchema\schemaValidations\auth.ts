import * as Yup from "yup";
import {
  confirmPasswordValidation,
  DOBValidation,
  emailValidation,
  passwordValidation,
} from "./common";

export const SignupSchemaValidation = Yup.object().shape({
  seekingFor: Yup.number().required("Please select your preference"),
  gender: Yup.number().required("Please select your gender"),
  username: Yup.string()
    .trim()
    .required("Username is required")
    .test("no-only-spaces", "Username cannot be only spaces", (value) => {
      return value ? value.trim().length > 0 : false;
    }),
  email: emailValidation,
  dob: DOBValidation,
  countryId: Yup.number().required("Country is required"),
  // city: Yup.string().required("City is required"),
  password: passwordValidation("Password"),
  confirmPassword: confirmPasswordValidation("password"),
  agreeTerms: Yup.boolean().oneOf([true], "You must accept the terms"),
});
