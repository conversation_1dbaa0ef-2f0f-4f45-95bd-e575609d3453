import React, { useState } from 'react';
import Select from 'react-select';
import { Form, InputGroup, Button, Image, Dropdown, Row, Col } from 'react-bootstrap';
import { SearchNormal1 } from 'iconsax-react';
import './styles.scss';
import ReactSelect from 'react-select'
import { IMAGE_PATHS } from '@/utils/image-path'
import AdvancedFilterPanel from '@/components/advancedfilterpanel/AdvancedFilterPanel';




const interestData = [
    { id: 1, label: 'Interest1', image: IMAGE_PATHS.memberList1 },
    { id: 2, label: 'Interest1', image: IMAGE_PATHS.memberList2 },
    { id: 3, label: 'Interest1', image: IMAGE_PATHS.memberList3 },
    { id: 4, label: 'Interest1', image: IMAGE_PATHS.memberList4 },
    { id: 5, label: 'Interest1', image: IMAGE_PATHS.memberList5 },
    { id: 6, label: 'Interest1', image: IMAGE_PATHS.memberList6 },
    { id: 7, label: 'Interest1', image: IMAGE_PATHS.memberList7 },
    { id: 8, label: 'Interest1', image: IMAGE_PATHS.memberList8 },
    { id: 9, label: 'Interest1', image: IMAGE_PATHS.memberList9 },
    { id: 10, label: 'Interest1', image: IMAGE_PATHS.memberList1 },
    { id: 11, label: 'Interest1', image: IMAGE_PATHS.memberList2 },
    { id: 12, label: 'Interest1', image: IMAGE_PATHS.memberList3 },
    { id: 13, label: 'Interest1', image: IMAGE_PATHS.memberList4 },
    { id: 14, label: 'Interest1', image: IMAGE_PATHS.memberList5 },
    { id: 15, label: 'Interest1', image: IMAGE_PATHS.memberList6 },
    { id: 16, label: 'Interest1', image: IMAGE_PATHS.memberList7 },
    { id: 17, label: 'Interest1', image: IMAGE_PATHS.memberList8 },
    { id: 18, label: 'Interest1', image: IMAGE_PATHS.memberList9 },
];

const SearchBar = () => {
    const [showFilters, setShowFilters] = useState(false);
    const [selectedAges, setSelectedAges] = useState([]);
    const [selectedDistance, setSelectedDistance] = useState([]);
    const [selected, setSelected] = useState<number[]>([]);
    const clearAll = () => setSelected([]);

    const toggleSelect = (id: number) => {
        setSelected((prev) =>
            prev.includes(id) ? prev.filter((i) => i !== id) : [...prev, id]
        );
    };

    const ageOptions = [
        { value: '18-20', label: '18–20' },
        { value: '20-30', label: '20–30' },
        { value: '30-40', label: '30–40' },
        { value: '40-50', label: '40–50' },
        { value: '50+', label: '50+' },
    ];

    const distanceOptions = [
        { value: '50km', label: '50 km' },
        { value: '100km', label: '100 km' },
        { value: '200km', label: '200 km' },
        { value: '500km', label: '500 km' },
        { value: 'any', label: 'Any' }
    ];

    const handleSelect = (value) => {
        setSelectedAges((prev) =>
            prev.includes(value) ? prev.filter((item) => item !== value) : [...prev, value]
        );
    };
    const handleDistanceSelect = (value) => {
        setSelectedDistance((prev) =>
            prev.includes(value) ? prev.filter((item) => item !== value) : [...prev, value]
        );
    };
    return (
        <>
            <div className="search-bar-wrapper d-flex flex-column gap-3 ">
                <div className='d-flex justify-content-between gap-3 align-items-start search-filters'>
                    <div className="form-input-group d-flex align-items-center flex-wrap">
                        <InputGroup className="search-input form-input">
                            <InputGroup.Text className=" border-0 p-0"><SearchNormal1 size="16" color="#141414" /></InputGroup.Text>
                            <Form.Control type="text" placeholder="Search by name or location" className="border-0 p-0" />
                        </InputGroup>
                        <Form.Group className="form-input">
                            <Dropdown onSelect={handleSelect}>
                                <Dropdown.Toggle variant="light" id="age-range-dropdown" className='custom-dropdown'>
                                    Age Range
                                </Dropdown.Toggle>
                                <Dropdown.Menu>
                                    {ageOptions.map((option) => (
                                        <Dropdown.Item key={option.value} eventKey={option.value}>
                                            <Form.Check
                                                type="checkbox"
                                                id={`age-${option.value}`}
                                                label={option.label}
                                                checked={selectedAges.includes(option.value)}
                                                onChange={() => handleSelect(option.value)}
                                                className="custom-checkbox"
                                            />
                                        </Dropdown.Item>
                                    ))}
                                </Dropdown.Menu>
                            </Dropdown>
                        </Form.Group>
                        <Form.Group className="form-input">
                            <Dropdown onSelect={handleDistanceSelect}>
                                <Dropdown.Toggle variant="light" id="distance-range-dropdown" className='custom-dropdown'>
                                    Distance
                                </Dropdown.Toggle>
                                <Dropdown.Menu>
                                    {distanceOptions.map((option) => (
                                        <Dropdown.Item key={option.value} eventKey={option.value}>
                                            <Form.Check
                                                type="checkbox"
                                                id={`distance-${option.value}`}
                                                label={option.label}
                                                checked={selectedDistance.includes(option.value)}
                                                onChange={() => handleDistanceSelect(option.value)}
                                                className="custom-checkbox"
                                            />
                                        </Dropdown.Item>
                                    ))}
                                </Dropdown.Menu>
                            </Dropdown>
                        </Form.Group>
                        <Form.Group className="form-input">
                            <Dropdown>
                                <Dropdown.Toggle variant="light" id="interest-dropdown" className='custom-dropdown interest'>
                                    Interests
                                </Dropdown.Toggle>
                                <Dropdown.Menu className="p-3 custom-interest-menu">
                                    <div className="d-flex flex-wrap gap-2 custom-interest-menu-data">
                                        {interestData.map((item) => (
                                            <div key={item.id} className='flex-1'>
                                                <div
                                                    className={`interest-card position-relative ${selected.includes(item.id) ? 'selected' : ''}`}
                                                    onClick={() => toggleSelect(item.id)}
                                                >
                                                    <Image src={item.image} rounded className="w-100 interest-img h-100 object-fit-cover" />
                                                    <div className="label-overlay">{item.label}</div>
                                                    {/* {selected.includes(item.id) && ( */}
                                                        <div className="checkmark">
                                                            {/* <svg width="20" height="20" viewBox="0 0 24 24" fill="#fff">
                                                                <circle cx="12" cy="12" r="10" stroke="#b936ad" strokeWidth="2" fill="#b936ad" />
                                                                <path d="M8 12l2 2 4-4" stroke="#fff" strokeWidth="2" fill="none" />
                                                            </svg> */}
                                                        </div>
                                                    {/* )} */}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                    <div className="d-flex align-items-center gap-3 mt-3 justify-content-end">
                                        <Button className='clear-filter-btn'>
                                            Clear Filter
                                        </Button>
                                        <Button variant="primary" >
                                            Search
                                        </Button>
                                    </div>
                                </Dropdown.Menu>
                            </Dropdown>
                        </Form.Group>
                        <div className='img-filter'>
                            <Form.Check
                                type="checkbox"
                                id="with-photos"
                                label="With Photos"
                                className="custom-checkbox"
                                defaultChecked
                            />
                        </div>

                        <Button variant="outline-secondary" className=" d-flex align-items-center gap-2 filter-btn" onClick={() => setShowFilters(true)}>
                            <span>Advanced Filter</span>
                            <Image src={IMAGE_PATHS.FilterIcon} alt="" />
                        </Button>

                    </div>
                    <Button variant="gradient-purple" className="d-flex align-items-center gap-2 search-btn">
                        <SearchNormal1 size="16" color="#ffffff" />
                        Search
                    </Button>
                </div>
                <div className='d-flex clear-filters justify-content-between gap-3 filter-result-dispaly'>
                    <div className='d-flex aling-items-center gap-2 flex-wrap'>
                        <div className='search-result'>
                            <Image src={IMAGE_PATHS.SearchFilterImg} alt="" />
                            18-20
                            <button className='bg-transparent border-0 p-0 close-btn'>
                                <Image src={IMAGE_PATHS.CloseIcon} alt="" />
                            </button>
                        </div>
                        <div className='search-result'>
                            <Image src={IMAGE_PATHS.SearchFilterImg1} alt="" />
                            18-20
                            <button className='bg-transparent border-0 p-0 close-btn'>
                                <Image src={IMAGE_PATHS.CloseIcon} alt="" />
                            </button>
                        </div>
                    </div>
                    <Button className='clear-filter-btn'>
                        Clear Filter
                    </Button>
                </div>
            </div>

            <AdvancedFilterPanel show={showFilters}
                onClose={() => setShowFilters(false)} />
        </>
    );
};

export default SearchBar;
