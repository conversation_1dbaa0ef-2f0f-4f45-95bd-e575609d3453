import { ROUTE_PATH } from "@/routes";
import { Form, Image } from "react-bootstrap";
import { Link } from "react-router-dom";
import "./styles.scss";
import { IMAGE_PATHS } from "@/utils/image-path";

const NotFound = () => {
  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column notfound-form"
      style={{ gap: "30px" }}
    >
      <div
        className="d-flex flex-column align-items-center"
        style={{ gap: "23px" }}
      >
        <div className="notfound-icon mb-2">
          <Image src={IMAGE_PATHS.BrokeHeart} alt="broke-heart-icon" />
        </div>
        <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
          Oops! Page Not Found
        </h1>
        <p className="mb-0 auth-form-description font-gray text-center">
          Sorry, the page you are looking for doesn't exist.
          <br className="d-lg-block d-none" />
          Maybe you took a wrong turn on your search for love!
          <br className="d-lg-block d-none" />
          Let's get you back to where the magic happens.
        </p>
      </div>
      <div className="website-form">
        <Form className="d-flex flex-column" style={{ gap: "30px" }}>
          <div
            className="action-btns d-flex flex-column"
            style={{ gap: "30px" }}
          >
            <Link
              to={ROUTE_PATH.HOME}
              className="d-flex justify-content-center align-items-center text-decoration-none submit-btn position-relative w-100 border-brown bg-transparent text-uppercase font-primary"
            >
              Go Back to Home
            </Link>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default NotFound;
