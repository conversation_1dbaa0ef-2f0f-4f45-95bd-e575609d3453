import { IMAGE_PATHS } from '@/utils/image-path';
import { Heart, Sms } from 'iconsax-react';
import React, { useState } from 'react';
import { Button, Col, Image, Offcanvas, Row } from 'react-bootstrap';
import FlirtPanel from '../flirtpanel/FlirtPanel';
import './styles.scss';
import useChatStore from '@/stores/useChatStore';

interface MembersCardProps {
  image: string;
  name: string;
  location: string;
  userId: string;
}

const MembersCard: React.FC<MembersCardProps> = ({ image, name, location, userId }) => {
  const [show, setShow] = useState(false);
  const [flirtShow, setFlirtShow] = useState(false);
  const { setActiveChatUser } = useChatStore();

  const handleClose = () => setShow(false);
  const handleShow = (e: any) => {
    e.stopPropagation();
    setShow(true);
  };
  const handleFlirtShow = (e: any) => {
    e.stopPropagation();
    setFlirtShow(true);
  };
  const doHide = () => {
    setFlirtShow(false);
  };

  const handleChatShow = (e: any) => {
    e.stopPropagation();
    setActiveChatUser({ id: userId, name, location });
  };

  return (
    <>
      <div className="member-card overflow-hidden" onClick={handleShow}>
        <div className="position-relative w-100">
          <Image src={image} className="member-img w-100" />
          <div className="favorite-icon">
            <Heart size="24" color="#ff0000" variant="Bold" />
          </div>
        </div>
        <div className="p-3 d-flex flex-column gap-3">
          <div className="d-flex justify-content-between gap-3">
            <div className="d-flex flex-column gap-1">
              <h3 className="mb-0 name">{name}</h3>
              <p className="mb-0 location">{location}</p>
            </div>
            <Button variant="outline-warning" className="email-btn">
              <Sms size="32" color="#f68507" variant="Bold" />
            </Button>
          </div>
          <div className="d-flex gap-2">
            <Button variant="outline-warning" className="w-100 first-btn" onClick={handleChatShow}>
              Chat
            </Button>
            <Button variant="gradient-purple" className="w-100 second-btn" onClick={handleFlirtShow}>
              Flirt
            </Button>
          </div>
        </div>
      </div>
      <Offcanvas show={show} onHide={handleClose} placement="end" className="user-profile-detail">
        <Offcanvas.Header closeButton className="user-profile-detail-header">
          <Offcanvas.Title>
            <div className="d-flex flex-column gap-1">
              <div className="name">{name}</div>
              <div className="location">{location}</div>
            </div>
          </Offcanvas.Title>
        </Offcanvas.Header>
        <Offcanvas.Body className="p-4 d-flex flex-column gap-3">
          <div className="d-flex flex-column gap-2 img-section">
            <div className="position-relative w-100">
              <Image src={IMAGE_PATHS.UserDetailImg1} className="large-img w-100" />
              <div className="favorite-icon">
                <Heart size="24" color="#ff0000" variant="Bold" />
              </div>
            </div>
            <div className="d-flex gap-2 image-scroll">
              <Image src={IMAGE_PATHS.UserDetailImg2} className="small-img w-100" />
              <Image src={IMAGE_PATHS.UserDetailImg3} className="small-img w-100" />
              <Image src={IMAGE_PATHS.UserDetailImg4} className="small-img w-100" />
              <Image src={IMAGE_PATHS.UserDetailImg5} className="small-img w-100" />
            </div>
          </div>
          <div className="d-flex justify-content-between btn-section">
            <Button variant="outline-warning" className="w-100 first-btn" onClick={handleChatShow}>
              Chat
            </Button>
            <Button variant="gradient-purple" className="w-100 second-btn" onClick={handleFlirtShow}>
              Flirt
            </Button>
            <Button variant="outline-warning" className="email-btn">
              <Sms size="32" color="#f68507" variant="Bold" />
            </Button>
          </div>
          <div className="details-section">
            <h6 className="title">About Me</h6>
            <p className="description mb-0">
              Maecenas dignissim justo eget nulla rutrum molestie. Maecenas lobortis sem dui, vel rutrum risus tincidunt ullamcorper. Proin eu enim metus. Vivamus sed libero ornare, tristique quam in, gravida enim. Nullam ut molestie arcu, at hendrerit elit. Morbi laoreet elit at ligula molestie, nec molestie mi blandit. Suspendisse cursus tellus sed augue ultrices, quis tristique nulla sodales.
            </p>
          </div>
          <div className="details-section">
            <h6 className="title">Personal Details</h6>
            <Row className="">
              <Col xs={6} className="d-flex flex-column details-section-info">
                Gender<span>Female</span>
              </Col>
              <Col xs={6} className="d-flex flex-column details-section-info">
                Relation Status<span>Married</span>
              </Col>
              <Col xs={6} className="d-flex flex-column details-section-info">
                Personality<span>Extrovert</span>
              </Col>
              <Col xs={6} className="d-flex flex-column details-section-info">
                Location<span>City of Westminster, United Kingdom</span>
              </Col>
            </Row>
          </div>
          <div className="details-section">
            <h6 className="title">Blond</h6>
            <Row className="">
              <Col xs={6} className="d-flex flex-column details-section-info">
                Hair Color<span>Female</span>
              </Col>
              <Col xs={6} className="d-flex flex-column details-section-info">
                Eye Color<span>Grey</span>
              </Col>
              <Col xs={6} className="d-flex flex-column details-section-info">
                Height<span>168 cm</span>
              </Col>
              <Col xs={6} className="d-flex flex-column details-section-info">
                Appearance<span>Good enough</span>
              </Col>
            </Row>
          </div>
        </Offcanvas.Body>
      </Offcanvas>
      <FlirtPanel show={flirtShow} doHide={doHide} />
    </>
  );
};

export default MembersCard;