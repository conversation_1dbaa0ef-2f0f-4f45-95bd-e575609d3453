import { ConfirmModal } from "@/components/modals";
import { BrowserRouter } from "react-router-dom";
import { ReactQueryProvider } from "./ReactQueryProvider";
import { RoutingProvider } from "./RoutingProvider";
import ToastProvider from "./ToastProvider";

export const AppProvider = () => {
  return (
    <ReactQueryProvider>
      <BrowserRouter basename={import.meta.env.VITE_BASE_URL}>
        <RoutingProvider />
        <ToastProvider />
        <ConfirmModal />
      </BrowserRouter>
    </ReactQueryProvider>
  );
};
