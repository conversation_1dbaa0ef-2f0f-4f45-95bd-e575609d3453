import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface UserInfo {
  user: any | null;
  access_token: string | null;
}

interface PayloadInterface {
  userInfo: UserInfo;
  rememberMeInfo?: Record<string, string>;
}

const initialState = {
  userInfo: {
    user: {} as any,
    access_token: "",
  },
  rememberMeInfo: {
    email: "",
  },
};

const userStore = (set: any) => ({
  ...initialState,
  setUserInfo: (data: UserInfo) =>
    set((state: PayloadInterface) => ({ ...state, userInfo: data })),
  setUserAvatar: (data: UserInfo) =>
    set((state: PayloadInterface) => ({
      ...state, userInfo: {
        ...state.userInfo,
        user: {
          ...state.userInfo.user,
          avatar: data
        }
      }
    })),
  setUserDetails: (data: UserInfo) =>
    set((state: PayloadInterface) => ({
      ...state, userInfo: {
        ...state.userInfo,
        user: data
      }
    })),
  setRememberMeInfo: (data: Record<string, string>) =>
    set((state: PayloadInterface) => ({ ...state, rememberMeInfo: data })),
  resetUserState: () =>
    set((state: PayloadInterface) => ({
      ...initialState,
      rememberMeInfo: state.rememberMeInfo,
    })),
});

const useUserStore = create(
  devtools(
    persist(userStore, {
      name: "user",
    }),
  ),
);

export const getAuthToken = () => {
  // access the zustand store outside of React.
  return useUserStore.getState().userInfo.access_token;
};

export const {
  setUserInfo,
  resetUserState,
  setRememberMeInfo,
  setUserAvatar,
  setUserDetails,
} = useUserStore.getState();

export default useUserStore;
