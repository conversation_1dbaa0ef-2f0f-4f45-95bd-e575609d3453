import useUtilStore, { setConfirmModalConfig } from "@/stores/util";
import { ConfirmModalConfig } from "@/types";
import { CloseCircle, Logout } from "iconsax-react";
import { Button, Modal } from "react-bootstrap";
import "./styles.scss";

const ConfirmModal: React.FC = () => {
  const modalConfig = useUtilStore((state) => state.confirmModalConfig);

  const handleClose = () => {
    setConfirmModalConfig({
      ...modalConfig,
      visible: false,
    });
  };

  const defaultContent = {
    heading: "Delete Item?",
    description: "Are you sure you want to delete this item?",
  };

  const {
    visible,
    data: {
      onSubmit = () => {},
      onClose = () => {},
      content = defaultContent,
      // icon = Trash,
      iconColor = "red",
      showCloseIcon = true,
      buttonText = "Confirm",
      showModalIcon = true,
      customStyling = {},
    },
  }: ConfirmModalConfig = modalConfig;

  return (
    <Modal
      show={visible}
      onHide={() => {
        if (showCloseIcon) {
          if (onClose) {
            onClose();
          }
          handleClose();
        }
      }}
      keyboard={false}
      centered
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        {showCloseIcon && (
          <Button
            variant="link"
            className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
            onClick={() => {
              if (onClose) {
                onClose();
              }
              handleClose();
            }}
          >
            <CloseCircle size={"40px"} color="#f9f9f9" />
          </Button>
        )}

        <div
          className="auth-form w-100 d-flex justify-content-center align-items-center flex-column m-0"
          style={{ gap: "30px" }}
        >
          {showModalIcon ? <Logout size={"100px"} color={iconColor} /> : null}

          <div className="d-flex flex-column" style={{ gap: "23px" }}>
            <h1
              className="auth-form-heading text-uppercase mb-0 text-center lh-1"
              style={{ ...customStyling?.heading }}
            >
              {content?.heading}
            </h1>
            <p
              className="mb-0 auth-form-description font-gray text-center lh-sm"
              dangerouslySetInnerHTML={{ __html: content?.description }}
            ></p>
          </div>

          <div className="website-form w-100">
            <form className="d-flex flex-column" style={{ gap: "30px" }}>
              <div
                className="action-btns d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="button"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  onClick={() => {
                    onSubmit();
                    handleClose();
                  }}
                >
                  {buttonText}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ConfirmModal;
