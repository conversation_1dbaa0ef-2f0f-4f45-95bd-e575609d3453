@use '@/variables' as *;

.header {
    background: $cream-color;
    padding: 16px 0px;

    &.not {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .main-logo {
        img {
            width: 100%;
            max-width: 100px;
        }
    }

    .navbar {
        &-nav {
            gap: 32px;

            .nav-link {
                color: $link-color;
                font-size: 18px;
                font-weight: 500;
                line-height: normal;
                padding: 0px 0px 2px;

                &.active {
                    background: var(--Button, linear-gradient(270deg, #B936AD 0%, #79259C 100%));
                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    border-bottom: 2px solid $secondary-color;
                }
            }
        }



        .notification-dropdown-wrapper {
            .notification-icon {
                width: 40px;
                height: 40px;
                min-width: 40px;
                position: relative;
                background: $white-color !important;

                .dot {
                    border-radius: 9000px;
                    border: 2px solid #FFF;
                    background: #E60022;
                    position: absolute;
                    right: 8px;
                    top: 7px;
                    width: 10px;
                    height: 10px;
                }

                img {
                    width: 20px;
                    height: 20px;
                    object-fit: contain;
                }
            }

            .dropdown-toggle::after {
                display: none;
            }

            .notification-dropdown {
                width: 25rem;
                border-radius: 20px;
                box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.25);
                top: 3rem;

                .notifications-title {
                    color: $black-color;
                    font-size: 20px;
                    font-weight: 700;
                    line-height: 1.3;
                }

                .mark-read-btn {
                    background: transparent;
                    color: $primary-color;
                    font-size: 13px;
                    font-weight: 600;
                    line-height: 1.4;
                    padding: 0px;
                    min-width: auto;
                }

                .notification-list {
                    max-height: 400px;
                    overflow: hidden;
                    overflow-y: auto;

                    .bg-unread {
                        background: $light-yellow;
                    }

                    .user-img {
                        min-width: 48px;
                        max-width: 48px;
                        object-fit: contain;
                        height: 48px;
                    }

                    .user-name {
                        color: $black-color;
                        font-size: 14px;
                        font-weight: 600;
                        line-height: 1.4;

                        span {
                            font-weight: 400;
                        }
                    }

                    .time {
                        color: $link-color;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 1.4;
                    }
                }

                .view-all {
                    border-top: 1px solid $light-gray-color;

                    a {
                        color: $primary-color;
                        font-size: 14px;
                        font-weight: 600;
                        line-height: 1.4;
                    }
                }
            }
        }

        .profile-dropdown-wrapper {
            .user-profile {
                cursor: pointer;
                background: transparent;
                padding: 0px;
                min-width: auto;

                &-name {
                    color: $black-color;
                    font-size: 16px;
                    font-weight: 700;
                    line-height: 1.3;
                }

                &-img {
                    width: 40px;
                    height: 40px;
                    min-width: 40px;
                    object-fit: contain;
                }
            }

            .dropdown-toggle::after {
                display: none;
            }

            .profile-dropdown-menu {
                width: 16rem;
                border-radius: 20px;
                box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.25);
                top: 3rem;

                .user-profile-img {
                    min-width: 50px;
                    height: 50px;
                    max-width: 50px;
                    width: 100%;
                    object-fit: cover;
                }

                .user-name {
                    color: $black-color;
                    font-size: 16px;
                    font-weight: 700;
                    line-height: 1.3;
                }

                .location {
                    color: $black-color;
                    font-size: 13px;
                    font-weight: 400;
                    line-height: 1.4;
                    letter-spacing: 0.26px;
                }

                .menu-display {
                    border-top: 1px solid $light-gray-color;
                    border-bottom: 1px solid $light-gray-color;
                }

                .item {
                    padding: 10px 16px;
                    gap: 12px;
                    color: $black-color;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 1.4;

                    &.logout-link {
                        color: #FF0101;
                    }
                }
            }
        }

        .coin-dropdown-wrapper {
            .dropdown-toggle {
                cursor: pointer;
                background: transparent;
                padding: 0px;
                min-width: auto;
            }

            .coins-number {
                color: $black-color;
                font-size: 18px;
                font-weight: 500;
                line-height: normal;
            }

            .dropdown-toggle::after {
                display: none;
            }

            .coin-details-dropdown {
                width: 25rem;
                border-radius: 20px;
                box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.25);
                top: 3rem;

                &-header {
                    border-bottom: 1px solid $light-gray-color;

                    .title {
                        color: $black-color;
                        font-size: 20px;
                        font-weight: 700;
                        line-height: 1.3;
                    }
                }

                .balance-details {
                    border-bottom: 1px solid $light-gray-color;
                    padding-bottom: 1rem;
                    margin-bottom: 1rem;

                    .plan {
                        color: $black-color;
                        font-size: 16px;
                        font-weight: 700;
                        line-height: normal;
                    }

                    .balance {
                        color: $link-color;
                        font-size: 14px;
                        font-weight: 500;
                        line-height: normal;
                    }

                    .total-coins {
                        color: $yellow-color;
                        font-size: 22px;
                        font-weight: 700;
                        line-height: normal;
                        letter-spacing: -0.44px;

                        .small-text {
                            color: $black-color;
                            font-size: 14px;
                            font-weight: 500;
                            line-height: normal;
                        }
                    }
                }

                .remaining-coins {
                    color: $yellow-color;
                    font-size: 14px;
                    font-weight: 600;
                    line-height: normal;

                    span {
                        color: $black-color;
                    }
                }

                .custom-progress {
                    height: 9px;
                    border-radius: 11px;
                    background: $light-yellow;

                    .progress-bar {
                        background-color: $yellow-color;
                    }
                }

                .plan-btn {
                    padding: 16px 20px;
                    border-radius: 12px;
                    color: $white-color;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: normal;
                    max-width: 160px;
                    min-width: auto;
                    margin-left: auto;
                }
            }
        }
    }
}

@media (max-width: '1599px') {
    .header {
        .navbar {
            &-nav {
                .nav-link {
                    font-size: 16px;
                }
            }
        }
    }
}

@media (max-width: '1199px') {
    .header {
        .navbar {
            &-nav {
                gap: 24px;

                .nav-link {
                    font-size: 16px;
                }
            }

            .profile-dropdown-wrapper {
                .user-profile {

                    &-name {
                        font-size: 14px;
                    }

                    &-img {
                        width: 35px;
                        height: 35px;
                        min-width: 35px;
                    }
                }
            }

            .notification-dropdown-wrapper {
                .notification-icon {
                    width: 35px;
                    height: 35px;
                    min-width: 35px;
                }

                .notification-dropdown {

                    .notifications-title {
                        font-size: 18px;
                    }
                }
            }

            .coin-dropdown-wrapper {

                .dropdown-toggle {
                    padding: 5px 12px !important;
                }

                .coins-number {
                    font-size: 16px;
                }

                .coin-details-dropdown {

                    &-header {

                        .title {
                            font-size: 18px;
                        }
                    }

                    .balance-details {

                        .total-coins {
                            font-size: 20px;
                        }
                    }

                    .plan-btn {
                        padding: 12px 20px;
                    }
                }
            }
        }
    }
}

@media (max-width: '991px') {
    .header {
        padding: 8px 0px;

        .main-logo {
            max-width: 80px;
        }

        .navbar {

            &-toggler {
                order: 3;
                border: 0;
                box-shadow: none;
                padding: 0;
                margin-left: 1rem;
            }

            &-collapse {
                order: 4;

                .navbar-nav {
                    padding: 1rem 0;
                    gap: 16px;
                }
            }

            &-right-menu {
                order: 2;
                margin-left: auto;
            }

            .profile-dropdown-wrapper {
                .user-profile {
                    &-name {
                        display: none;
                    }
                }
            }
        }
    }
}

@media (max-width: '767px') {
    .header {
        .navbar {
            &-nav {
                .nav-link {
                    font-size: 14px;
                }
            }

            .notification-dropdown-wrapper {
                .notification-dropdown {
                    width: 20rem;
                    right: -80px;
                    left: auto;
                }
            }

            .coin-dropdown-wrapper {
                .coin-details-dropdown {
                     width: 21rem;
                    right: -130px;
                    left: auto;
                }
            }
        }
    }
}