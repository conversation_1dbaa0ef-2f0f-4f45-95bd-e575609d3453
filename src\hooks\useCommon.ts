import { useLogoutMutation } from "@/api";
import { ROUTE_PATH } from "@/routes";
import { resetUserState } from "@/stores";
import { useNavigate } from "react-router-dom";

export const useLogout = () => {
    const navigate = useNavigate();
    const { mutateAsync: logout } = useLogoutMutation();

    const onLogout = async () => {
        try {
            await logout();
            resetUserState();
            navigate(ROUTE_PATH.LOGIN);
        } catch (err) {
            console.log(err)
        }
    }

    return onLogout;
}