import React from 'react'
import './styles.scss'
import { Col } from 'react-bootstrap';
interface PlanCardProps {
    title: string;
    coins: number;
    originalPrice: number;
    currentPrice: number;
    isActive: boolean;
    index: number;
}


const PlanCard: React.FC<PlanCardProps> = ({ title, coins, originalPrice, currentPrice, isActive }) => {
    return (

        <Col xs={12} md={6} lg={4} xl={3} className="mb-4">
            <div className={`plan-card h-100 ${isActive ? 'active' : ''}`}>
                <div className="card-header d-flex align-items-center justify-content-center">
                    <h5 className="title mb-0">{title}</h5>
                </div>
                <div className='plan-card-content'>
                    <div className='d-flex gap-2 plan-card-detail'>
                        <div className="icon-wrapper d-flex align-items-center justify-content-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="42" height="42" viewBox="0 0 42 42" fill="none">
                                <path d="M28.731 1.19295C22.06 1.19295 16.652 4.17519 16.652 7.85932C16.652 11.5434 22.0615 14.5272 28.731 14.5272C35.402 14.5272 40.8099 11.5434 40.8099 7.85932C40.8099 4.17519 35.4004 1.19295 28.731 1.19295ZM40.8099 9.83975C40.8068 13.5239 35.402 16.5077 28.731 16.5077C22.0785 16.5077 16.683 13.5563 16.652 9.88922V12.4973C16.652 14.0712 17.66 15.512 19.3112 16.653C20.9809 17.1075 22.4681 17.7383 23.7065 18.536C25.2416 18.9256 26.9299 19.1637 28.731 19.1637C35.402 19.1637 40.8099 16.1815 40.8099 12.4973V9.83975ZM40.8099 14.4778C40.8099 18.1619 35.402 21.1457 28.731 21.1457C27.9193 21.1457 27.1309 21.0854 26.364 21.0003C26.9691 21.8153 27.3819 22.7566 27.5715 23.7538C27.9564 23.7754 28.3367 23.8017 28.731 23.8017C35.402 23.8017 40.8099 20.8195 40.8099 17.1353V14.4778ZM13.2709 18.1974C6.59994 18.1974 1.19202 21.1812 1.19202 24.8654C1.19202 28.5495 6.60148 31.5333 13.2709 31.5333C19.9419 31.5333 25.3499 28.5495 25.3499 24.8654C25.3499 21.1812 19.9404 18.1974 13.2709 18.1974ZM40.8099 19.1158C40.8068 22.7999 35.402 25.7837 28.731 25.7837C28.3429 25.7837 27.9502 25.7558 27.5715 25.7342C27.5545 25.8502 27.5452 26.0048 27.5235 26.1207C27.6008 26.3542 27.6673 26.5845 27.6673 26.8473V28.3933C28.0183 28.4103 28.3708 28.4397 28.731 28.4397C35.402 28.4397 40.8099 25.4575 40.8099 21.7733V19.1158ZM40.8099 23.7538C40.8068 27.4379 35.402 30.4217 28.731 30.4217C28.3429 30.4217 27.9502 30.3939 27.5715 30.3722C27.5545 30.4882 27.5452 30.6428 27.5235 30.7587C27.6008 30.9906 27.6673 31.2225 27.6673 31.4853V33.0313C28.0183 33.0483 28.3708 33.0777 28.731 33.0777C35.402 33.0777 40.8099 30.0955 40.8099 26.4114V23.7538ZM25.3499 26.8458C25.3468 30.5299 19.9419 33.5137 13.2709 33.5137C6.61849 33.5137 1.22294 30.5624 1.19202 26.8937V29.5034C1.19202 33.1875 6.59839 36.1713 13.2709 36.1713C19.9435 36.1713 25.3499 33.1875 25.3499 29.5034V26.8458ZM25.3499 31.4838C25.3468 35.1679 19.9419 38.1517 13.2709 38.1517C6.61849 38.1517 1.22294 35.2004 1.19202 31.5317V34.1414C1.19202 37.8255 6.59839 40.8093 13.2709 40.8093C19.9435 40.8093 25.3499 37.8255 25.3499 34.1414V31.4838Z" fill="#FF8400" />
                            </svg>
                        </div>
                        <div className='d-flex flex-column'>
                            <h3 className="coins-number mb-0">{coins}</h3>
                            <div className="coins-title">Coins</div>
                        </div>
                    </div>
                    <div className='d-flex flex-column gap-2 mb-5'>
                        <div className="text-decoration-line-through original-price">A${originalPrice.toFixed(2)}</div>
                        <h5 className="current-price mb-0">A${currentPrice.toFixed(2)}</h5>
                    </div>
                    <button className="buy-btn w-100">Buy Now</button>
                </div>
            </div>
        </Col>

    )
}

export default PlanCard