import SearchBar from '@/components/common/Search'
import React from 'react'
import { Container } from 'react-bootstrap'
import './styles.scss'
import MembersCard from "@/components/memberscard/MembersCard"
import { IMAGE_PATHS } from '@/utils/image-path'
const Favorites: React.FC = () => {
    return (
        <div className='favorites'>
            <Container fluid>
                <div className='d-flex flex-column gap-3'>
                    <SearchBar />
                    <div className="d-flex flex-wrap members-list-content ">
                        <MembersCard image={IMAGE_PATHS.memberList1} />
                        <MembersCard image={IMAGE_PATHS.memberList2} />
                        <MembersCard image={IMAGE_PATHS.memberList3} />
                        <MembersCard image={IMAGE_PATHS.memberList4} />
                        <MembersCard image={IMAGE_PATHS.memberList5} />
                        <MembersCard image={IMAGE_PATHS.memberList6} />
                        <MembersCard image={IMAGE_PATHS.memberList7} />
                        <MembersCard image={IMAGE_PATHS.memberList8} />
                        <MembersCard image={IMAGE_PATHS.memberList9} />
                        <MembersCard image={IMAGE_PATHS.memberList1} />
                        <MembersCard image={IMAGE_PATHS.memberList2} />
                        <MembersCard image={IMAGE_PATHS.memberList3} />
                        <MembersCard image={IMAGE_PATHS.memberList4} />
                        <MembersCard image={IMAGE_PATHS.memberList5} />
                        <MembersCard image={IMAGE_PATHS.memberList6} />
                        <MembersCard image={IMAGE_PATHS.memberList7} />
                        <MembersCard image={IMAGE_PATHS.memberList8} />
                        <MembersCard image={IMAGE_PATHS.memberList9} />
                    </div>
                </div>
            </Container>
        </div>
    )
}

export default Favorites