import { useStaticPage } from "@/api/utils.api";
import { ROUTE_PATH } from "@/routes";
import parse from "html-react-parser";
import React from "react";
import { But<PERSON>, Spinner } from "react-bootstrap";
import { Link, useParams } from "react-router-dom";
import "./styles.scss";
import { useAutoScroll } from "@/hooks";

const StaticPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const { data: pageData, isLoading, error } = useStaticPage(slug || "");

  useAutoScroll();

  if (isLoading) {
    return (
      <div className="static-page-container d-flex flex-column justify-content-center align-items-center">
        <Spinner animation="border" role="status" className="mb-3">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="text-white">Loading page content...</p>
      </div>
    );
  }

  if (error || !pageData?.translation) {
    return (
      <div className="static-page-container d-flex flex-column justify-content-center align-items-center">
        <div className="text-center">
          <h3 className="text-danger mb-3">Page Not Found</h3>
          <p className="mb-4 text-white">
            The requested page could not be found.
          </p>
          <Link to={ROUTE_PATH.SIGNUP}>
            <Button variant="primary">Back to Signup</Button>
          </Link>
        </div>
      </div>
    );
  }

  const { translation } = pageData;

  return (
    <div className="static-page-container d-flex flex-column justify-content-between">
      <div className="d-flex flex-column gap-4">
        <div className="static-page-heading text-center">
          <h3 className="color-primary fw-bold fs-1">
            <span>{translation.title}</span>
          </h3>
        </div>

        <div className="static-page-content">
          <div className="content-wrapper">
            {parse(translation.description || "")}
          </div>
        </div>
      </div>

      <div className="text-center mt-3 mt-sm-4">
        <Link to={ROUTE_PATH.SIGNUP}>
          <Button variant="primary" className="w-100">
            Back to Signup
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default StaticPage;
