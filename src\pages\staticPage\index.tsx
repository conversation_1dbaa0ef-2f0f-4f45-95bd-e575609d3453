import { useStaticPage } from "@/api/utils.api";
import { useAutoScroll } from "@/hooks";
import parse from "html-react-parser";
import React from "react";
import { Spinner } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import "./styles.scss";

const StaticPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const { data: pageData, isLoading, error } = useStaticPage(slug || "");

  useAutoScroll();

  const handleGoBack = () => {
    navigate(-1);
  };

  if (isLoading) {
    return (
      <div className="static-page-container">
        <button
          onClick={handleGoBack}
          className="back-button position-absolute text-light fw-bold"
        >
          Back
        </button>
        <div className="d-flex flex-column justify-content-center align-items-center h-100">
          <Spinner animation="border" role="status" className="mb-3">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="text-white">Loading page content...</p>
        </div>
      </div>
    );
  }

  if (error || !pageData?.translation) {
    return (
      <div className="static-page-container">
        <button
          onClick={handleGoBack}
          className="back-button position-absolute text-light fw-bold"
        >
          Back
        </button>
        <div className="d-flex flex-column justify-content-center align-items-center h-100">
          <div className="text-center">
            <h3 className="text-danger mb-3">Page Not Found</h3>
            <p className="mb-4 text-white">
              The requested page could not be found.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const { translation } = pageData;

  return (
    <div className="static-page-container">
      <button
        onClick={handleGoBack}
        className="back-button position-absolute text-light fw-bold"
      >
        Back
      </button>

      <div className="d-flex flex-column gap-4 h-100">
        <div className="static-page-heading text-center">
          <h3 className="color-primary fw-bold fs-1">
            <span>{translation.title}</span>
          </h3>
        </div>

        <div className="static-page-content">
          <div className="content-wrapper">
            {parse(translation.description || "")}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StaticPage;
