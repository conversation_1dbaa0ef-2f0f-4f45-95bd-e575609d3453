import './styles.scss';
import Header from "@/components/common/Header";
import SearchBar from "@/components/common/Search";
import { Container } from "react-bootstrap";
import MembersCard from "@/components/memberscard/MembersCard";
import { IMAGE_PATHS } from '@/utils/image-path';
import ChatPopup from '@/components/chatbot/ChatPopup';

// Sample user data with IDs (replace with API call in production)
const users = [
  { id: '1', name: '<PERSON>-up, 23', image: IMAGE_PATHS.memberList1, location: 'Bridepath, Canada' },
  { id: '2', name: '<PERSON>, 25', image: IMAGE_PATHS.memberList2, location: 'Toronto, Canada' },
  { id: '3', name: '<PERSON>, 21', image: IMAGE_PATHS.memberList3, location: 'Vancouver, Canada' },
  { id: '4', name: '<PERSON>, 27', image: IMAGE_PATHS.memberList4, location: 'Montreal, Canada' },
  { id: '5', name: '<PERSON>, 30', image: IMAGE_PATHS.memberList5, location: 'Ottawa, Canada' },
  { id: '6', name: '<PERSON>, 22', image: IMAGE_PATHS.memberList6, location: 'Calgary, Canada' },
  { id: '7', name: '<PERSON>, 28', image: IMAGE_PATHS.memberList7, location: 'Edmonton, Canada' },
  { id: '8', name: 'Emma Davis, 24', image: IMAGE_PATHS.memberList8, location: 'Halifax, Canada' },
  { id: '9', name: 'Frank Miller, 26', image: IMAGE_PATHS.memberList9, location: 'Winnipeg, Canada' },
  { id: '10', name: 'Grace Taylor, 29', image: IMAGE_PATHS.memberList1, location: 'Quebec, Canada' },
  { id: '11', name: 'Henry White, 23', image: IMAGE_PATHS.memberList2, location: 'Saskatoon, Canada' },
  { id: '12', name: 'Isabella Clark, 25', image: IMAGE_PATHS.memberList3, location: 'Regina, Canada' },
  { id: '13', name: 'James Harris, 27', image: IMAGE_PATHS.memberList4, location: 'Victoria, Canada' },
  { id: '14', name: 'Kelly Adams, 22', image: IMAGE_PATHS.memberList5, location: 'London, Canada' },
  { id: '15', name: 'Liam Turner, 30', image: IMAGE_PATHS.memberList6, location: 'Hamilton, Canada' },
  { id: '16', name: 'Mia Garcia, 24', image: IMAGE_PATHS.memberList7, location: 'Windsor, Canada' },
  { id: '17', name: 'Noah Martinez, 26', image: IMAGE_PATHS.memberList8, location: 'Kelowna, Canada' },
  { id: '18', name: 'Olivia Brown, 28', image: IMAGE_PATHS.memberList9, location: 'Guelph, Canada' },
];

const Home = () => {
  return (
    <div>
      <Header />
      <div className="banner-img">
        <Container fluid>
          <div className="d-flex flex-column gap-2 mb-3">
            <h2 className="title mb-0">Find a chat partner</h2>
            <p className="description mb-0">Discover the best chat partner.</p>
          </div>
          <SearchBar />
        </Container>
      </div>
      <div className="members-list">
        <Container fluid>
          <div className="d-flex flex-wrap members-list-content">
            {users.map((user) => (
              <MembersCard
                key={user.id}
                image={user.image}
                name={user.name}
                location={user.location}
                userId={user.id}
              />
            ))}
          </div>
        </Container>
      </div>
      <ChatPopup />
    </div>
  );
};

export default Home;