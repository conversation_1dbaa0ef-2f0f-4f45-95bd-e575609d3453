.static-page-container {
  width: 100%;
  padding: 2rem;
  background: transparent;
  color: white;
}

.static-page-heading {
  margin-bottom: 2rem;

  h3 {
    color: white;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0;
  }
}

.static-page-content {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 2rem;

  .content-wrapper {
    padding: 0;
    background: transparent;
    border: none;

    // Style the parsed HTML content with white text
    h1, h2, h3, h4, h5, h6 {
      color: white;
      margin-bottom: 1rem;
      font-weight: 600;
    }

    p {
      margin-bottom: 1rem;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.9);
    }

    ul, ol {
      margin-bottom: 1rem;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        line-height: 1.5;
        color: rgba(255, 255, 255, 0.9);
      }
    }

    strong, b {
      font-weight: 600;
      color: white;
    }

    em, i {
      font-style: italic;
      color: rgba(255, 255, 255, 0.9);
    }

    a {
      color: #66b3ff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
        color: #99ccff;
      }
    }

    // Custom scrollbar for dark theme
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .static-page-container {
    padding: 1rem;
  }

  .static-page-heading h3 {
    font-size: 1.5rem;
  }

  .static-page-content .content-wrapper {
    font-size: 0.9rem;
  }
}
