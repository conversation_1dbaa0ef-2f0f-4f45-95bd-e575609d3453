@use "@/variables" as *;

.static-page-container {
  width: 100%;
  padding: 2rem;
  background: transparent;
  position: relative;
}

.back-button {
  top: 1rem;
  left: 1rem;
  line-height: 1;
  border-radius: 8px;
  padding: 15px 25px;
  border: 0;
  background: linear-gradient(270deg, $primary-color 0%, $secondary-color 100%);
  transition: 0.3s ease-in-out;
  z-index: 10;

  &:hover {
    background: linear-gradient(
      270deg,
      $secondary-color 0%,
      $primary-color 100%
    );
  }
}

.static-page-heading {
  margin-bottom: 2rem;
}

.static-page-content {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 2rem;

  .content-wrapper {
    padding: 0;
    background: transparent;
    border: none;
  }
}

@media (max-width: 768px) {
  .static-page-container {
    padding: 1rem;
    padding-top: 4rem;
  }

  .back-button {
    top: 0.5rem;
    left: 0.5rem;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .static-page-heading h3 {
    font-size: 1.5rem;
  }

  .static-page-content .content-wrapper {
    font-size: 0.9rem;
  }
}
