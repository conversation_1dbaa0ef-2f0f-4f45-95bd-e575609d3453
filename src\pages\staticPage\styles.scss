.static-page-content {
  max-height: 400px;
  overflow-y: auto;
  
  .content-wrapper {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
    
    // Style the parsed HTML content
    h1, h2, h3, h4, h5, h6 {
      color: #333;
      margin-bottom: 1rem;
      font-weight: 600;
    }
    
    p {
      margin-bottom: 1rem;
      line-height: 1.6;
      color: #555;
    }
    
    ul, ol {
      margin-bottom: 1rem;
      padding-left: 1.5rem;
      
      li {
        margin-bottom: 0.5rem;
        line-height: 1.5;
        color: #555;
      }
    }
    
    strong, b {
      font-weight: 600;
      color: #333;
    }
    
    em, i {
      font-style: italic;
    }
    
    a {
      color: #007bff;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .static-page-content {
    max-height: 300px;
    
    .content-wrapper {
      padding: 0.75rem;
      font-size: 0.9rem;
    }
  }
}
