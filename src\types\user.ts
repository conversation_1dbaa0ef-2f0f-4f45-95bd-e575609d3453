
export interface UserInterface {
  id: number;
  name: string | null,
  username: string,
  email: string,
  status: string,
  isEmailVerified: boolean,
  avatar: string,
}

export interface OrganizationInterface {
  id: number;
  user_id: number;
  title: string;
  logo: string;
  settings: string;
  created_at: string;
  updated_at: string;
}

export interface OrganizationMemberInterface {
  id: number;
  organization_id: number;
  member_id: number;
  role: string;
  status: string;
  created_at: string;
  updated_at: string;
  activation_token: string;
  organization: OrganizationInterface;
}
