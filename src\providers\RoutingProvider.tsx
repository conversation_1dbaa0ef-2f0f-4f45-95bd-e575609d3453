import AuthLayout from "@/components/layouts/authLayout";
import { NotFound } from "@/pages";
import { PrivateRoutes, PublicRoutes } from "@/routes";
import useUserStore from "@/stores/user";
import { Route, Routes } from "react-router-dom";

export const RoutingProvider = () => {
  const access_token = useUserStore((state) => state.userInfo.access_token);
  return (
    <Routes>
      {access_token ? PrivateRoutes() : PublicRoutes()}
      <Route element={<AuthLayout />}>
        <Route path="*" element={<NotFound />} />
      </Route>
    </Routes>
  );
};
