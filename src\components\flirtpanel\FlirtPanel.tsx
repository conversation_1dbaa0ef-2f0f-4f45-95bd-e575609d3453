import React, { useState } from 'react';
import { Offcanvas } from 'react-bootstrap';
import './styles.scss'


const flirtMessages = Array(10).fill("Good morning. How are you? Can we meet yesterday?....");

const FlirtPanel: React.FC<any> = ({ show, doHide }: any) => {

  const handleClose = () => {
    doHide();
  }

  return (
    <>
      <Offcanvas show={show} onHide={handleClose} placement="end" className="flirt-offcanvas">
        <Offcanvas.Header closeButton>
          <Offcanvas.Title>Flirt with Me</Offcanvas.Title>
        </Offcanvas.Header>
        <Offcanvas.Body className="d-flex flex-column gap-3">
          {flirtMessages.map((msg, idx) => (
            <div key={idx} className="flirt-message rounded-3 p-3 ">
              {msg}
            </div>
          ))}
        </Offcanvas.Body>
      </Offcanvas>
    </>
  );
};

export default FlirtPanel;
