import React from "react";

interface MemoizedRadioProps {
  options: Array<{ value: any; label: string }>;
  value: any;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  name?: string;
  className?: string;
  disabled?: boolean;
}

const MemoizedRadio: React.FC<MemoizedRadioProps> = React.memo(
  ({ options, value, onChange = () => {}, name, className, disabled }) => {
    // Helper to handle number conversion for Formik
    const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let newValue: any = e.target.value;
      // If the first option value is a number, convert
      if (options.length > 0 && typeof options[0].value === "number") {
        newValue = Number(newValue);
        // Patch the event to have the correct value type for Formik
        e.target.value = newValue as any;
      }
      onChange(e);
    };
    return (
      <div className={className || "d-flex gap-2"}>
        {options.map((option) => (
          <div className="form-check" key={option.value}>
            <input
              className="form-check-input"
              type="radio"
              name={name}
              id={`${name}-radio-${option.value}`}
              value={option.value}
              checked={value === option.value}
              onChange={handleRadioChange}
              disabled={disabled}
            />
            <label
              className="form-check-label"
              htmlFor={`${name}-radio-${option.value}`}
            >
              {option.label}
            </label>
          </div>
        ))}
      </div>
    );
  }
);

export default MemoizedRadio;
