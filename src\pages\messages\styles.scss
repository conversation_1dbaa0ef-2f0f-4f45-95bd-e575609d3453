@use '@/variables' as *;

.messages {
    padding: 24px 0;

    .chat-area {
        border-radius: 20px;
        border: 1px solid $light-gray-color;
        background: $white-color;
        height: calc(100vh - 125px);

        .sidebar {
            width: 30%;
            max-width: 480px;
            border-right: 1px solid $light-gray-color;

            .people-search-bar {
                margin: 20px;

                svg {
                    position: absolute;
                    left: 16px;
                    top: 50%;
                    transform: translateY(-50%);
                }

                input {
                    border-color: $light-gray-color;
                    padding: 8px 16px 8px 52px;
                    width: -webkit-fill-available;
                    height: 55px;
                    box-shadow: none;

                    &:focus {
                        border-color: $primary-color;
                    }
                }
            }

            .people-list {
                flex-grow: 1;
                overflow-y: auto;
                border-bottom-left-radius: 20px;

                .person {
                    gap: 10px;
                    padding: 20px;
                    cursor: pointer;
                    transition: background 0.2s;
                    border-bottom: 1px solid $light-gray-color;

                    img {
                        width: 50px;
                        height: 50px;
                        object-fit: cover;
                    }

                    .preview {
                        font-size: 14px;
                    }

                    .time {
                        font-size: 11px;
                        color: $black-color;
                    }

                    &.active {
                        background-color: $light-yellow;
                    }

                    &:hover {
                        background-color: $light-yellow;
                    }

                    &:last-child {
                        border-bottom: 0;
                    }
                }
            }

        }

        .chat-window {
            flex: 1;

            .chat-header {
                padding: 15px 20px;
                gap: 15px;
                border-top-right-radius: 20px;
                background: linear-gradient(270deg, #B936AD 0%, #79259C 100%);

                img {
                    width: 50px;
                    height: 50px;
                    object-fit: cover;
                    border: 2px solid $white-color;
                }

                .name {
                    font-weight: 500;
                    color: $white-color;
                }

                .location {
                    font-size: 14px;
                    color: $white-color;
                }
            }

            .chat-messages {
                flex-grow: 1;
                overflow-y: auto;
                padding: 20px;

                .message {
                    margin-bottom: 12px;
                    gap: 8px;

                    img {
                        width: 40px;
                        height: 40px;
                        object-fit: cover;
                    }

                    &-content {
                        flex: 1;
                    }

                    &.sent {
                        text-align: right;
                        justify-content: end;

                        .bubble {
                            background-color: $primary-color;
                            color: white;
                            font-size: 14px;
                            padding: 10px 16px;
                            border-radius: 12px 12px 0px 12px;
                            margin-bottom: 6px;
                            margin-left: auto;
                            width: fit-content;
                            max-width: 80%;
                        }
                    }

                    &.received {
                        text-align: left;

                        .bubble {
                            font-size: 14px;
                            padding: 10px 16px;
                            margin-bottom: 6px;
                            border-radius: 0px 12px 12px 12px;
                            border: 1px solid $light-gray-color;
                            background: $white-color;
                            width: fit-content;
                            max-width: 80%;
                        }
                    }

                    .timestamp {
                        font-size: 11px;
                        color: $black-color;
                    }
                }

                .date-divider {
                    gap: 12px;
                    margin: 0 20px 12px 20px;

                    div {
                        border-radius: 42px;
                        background: $white-color;
                        box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.10);
                        padding: 8px 16px;
                        font-size: 11px;
                        color: $black-color;
                        white-space: nowrap;
                    }

                    hr {
                        flex: 1;
                        margin: 0;
                        border-top: 1px solid #ccc;
                    }
                }
            }

            .chat-input {
                border-top: 1px solid $light-gray-color;
                padding: 20px 16px;

                .input-group {
                    display: flex;
                    gap: 16px;

                    input {
                        box-shadow: none;
                        padding: 0;
                    }

                    button {
                        min-width: auto;

                        &.bg-transparent {
                            background: transparent;
                        }

                        &.send-btn {
                            width: 32px;
                            height: 32px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                    }
                }
            }
        }
    }

    .back-btn {
        background: transparent;
        min-width: fit-content;
        color: $black-color;
        font-size: 14px;

        &:hover {
            background: transparent;
            color: $black-color;
        }
    }
}

@media (max-width: '1199px') {
    .messages {

        .chat-area {
            .sidebar {
                width: 40%;
                max-width: 350px;
            }
        }
    }
}

@media (max-width: 767px) {
    .messages {
        .chat-area {
            flex-direction: column;
            height: auto;

            .sidebar {
                width: 100%;
                max-width: 100%;
                border-right: none;

                .people-search-bar {
                    margin: 15px;

                    input {
                        height: 50px;
                        padding: 8px 16px 8px 42px;
                    }

                    svg {
                        width: 20px;
                    }
                }

                .people-list {
                    height: calc(100vh - 180px);
                    
                    .person {
                        padding: 15px;

                        img {
                            width: 45px;
                            height: 45px;
                        }

                        .preview {
                            font-size: 13px;
                        }

                        h6 {
                            font-size: 15px;
                            margin-bottom: 3px !important;
                        }

                        .time {
                            font-size: 10px;
                        }
                    }
                }
            }

            .chat-window {
                width: 100%;

                .chat-header {
                    border-top-left-radius: 20px;
                    padding: 12px 15px;

                    img {
                        width: 45px;
                        height: 45px;
                    }

                    .name {
                        font-size: 16px;
                        margin-bottom: 2px !important;
                    }

                    .location {
                        font-size: 12px;
                    }
                }

                .chat-messages {
                    padding: 15px;
                    height: calc(100vh - 270px);

                    .date-divider {
                        div {
                            padding: 6px 12px;
                            font-size: 10px;
                        }
                    }

                    .message {

                        .timestamp {
                            font-size: 10px;
                        }

                        .bubble {
                            font-size: 13px !important;
                            padding: 8px 12px !important;
                        }
                    }
                }

                .chat-input {
                    padding: 15px;

                    .input-group {
                        gap: 10px;

                        input {
                            font-size: 14px;
                        }

                        button {
                            svg {
                                width: 20px;
                            }

                            &.send-btn {
                                width: 28px;
                                height: 28px;
                                margin-left: 4px !important;
                            }
                        }
                    }
                }
            }
        }
    }
}