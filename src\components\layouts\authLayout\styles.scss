@use "@/variables" as *;

.auth-layout {
  height: 100dvh;
  overflow: hidden;

  .logo-container {
    z-index: 10;
    filter: invert(1);

    img {
      max-width: 120px;
      height: auto;
    }
  }

  &-left {
    position: sticky;
    top: 0;
    flex: 1;
    height: 100dvh;
    max-width: 35%;
    background-image: url("../../../assets/images/auth-bg.svg");
    background-size: cover;
    background-repeat: no-repeat;
  }

  &-right {
    flex: 1;
    flex-flow: wrap;
    padding: 2rem;
    height: 100dvh;
    overflow-y: auto;
    max-width: 65%;
    background: radial-gradient(
      88.98% 88.98% at 50% 50%,
      #ffeed6 0%,
      #dbb585 100%
    );

    .auth-form {
      max-width: 560px;
      padding: 30px;
      border-radius: 24px;
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);

      &-heading {
        h3 {
          font-size: 45px;
          text-transform: capitalize;

          span {
            color: $primary-color;
            font-weight: 800;
          }
        }

        p {
          font-size: 18px;
        }
      }

      .form-check-label {
        font-size: 14px;
        text-align: left;

        a {
          text-decoration: underline;
          color: $text-color;
          font-weight: 400;
          white-space: nowrap;
        }
      }

      .forgot-link {
        color: $black-color;
        font-size: 15px;

        &:hover {
          color: #000;
        }
      }

      a {
        color: $primary-color;
        font-weight: 600;

        &:hover {
          color: $secondary-color;
        }
      }

      .btn {
        height: 60px;
      }
    }
  }
}

@media (max-width: "1199px") {
  .auth-layout {
    &-right {
      .auth-form {
        &-heading {
          h3 {
            font-size: 38px;
          }

          p {
            font-size: 16px;
          }
        }

        .form-check-label {
          font-size: 12px;
        }

        .forgot-link {
          font-size: 12px;
        }
      }
    }
  }
}

@media (max-width: "991px") {
  .auth-layout {
    height: auto;
    min-height: 100dvh;

    &-left {
      display: none;
    }

    &-right {
      width: 100% !important;
      max-width: 100%;
      height: auto;
    }

    .logo-container {
      img {
        max-width: 80px;
      }
    }
  }
}

@media (max-width: "575px") {
  .auth-layout {
    &-left {
      display: none;
    }
    &-right {
      padding: 5rem 1rem;

      .auth-form {
        padding: 30px 20px;

        &-heading {
          h3 {
            font-size: 28px;
          }

          p {
            font-size: 14px;
          }
        }

        .btn {
          height: 50px;
        }
      }
    }
  }
}
