import { useChangePasswordMutation } from "@/api";
import Password<PERSON>ield from "@/components/common/PasswordField";
import {
  confirmPasswordValidation,
  passwordValidation,
} from "@/formSchema/schemaValidations";
import { ChangePasswordInterface } from "@/types";
import { useFormik } from "formik";
import { Button, Form } from "react-bootstrap";
import toast from "react-hot-toast";
import * as Yup from "yup";

const ChangePassword = () => {
  const { mutateAsync: changePassword, isPending } =
    useChangePasswordMutation();

  const formik = useFormik({
    initialValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
    validationSchema: Yup.object().shape({
      currentPassword: passwordValidation("Current Password"),
      newPassword: passwordValidation("New Password").notOneOf(
        [Yup.ref("currentPassword")],
        "New password must be different from current password."
      ),
      confirmPassword: confirmPasswordValidation("newPassword"),
    }),
    onSubmit: async (
      values: ChangePasswordInterface,
      { setSubmitting, resetForm }
    ) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { confirmPassword, ...payload } = values;
      try {
        const response: any = await changePassword(payload);
        if (response?.success) {
          toast.success(response?.message);
          resetForm();
        }
      } catch (error: any) {
        console.log(error);
        toast.error("Something went wrong");
      } finally {
        setSubmitting(false);
      }
    },
  });

  return (
    <div className="change-password">
      <div className="content-heading">
        <h4 className="fw-bold">Change Password</h4>
        <p className="mb-0">
          Donec vitae mi vulputate, suscipit urna in, malesuada nisl.
          Pellentesque laoreet pretium nisl, et pulvinar massa eleifend sed.
          Curabitur maximus mollis.
        </p>
      </div>
      <Form onSubmit={formik.handleSubmit}>
        <div className="form-input-group d-flex flex-column">
          <PasswordField
            label="Current Password"
            name="currentPassword"
            placeholder="Password (8 or more characters)"
            values={formik.values}
            handleChange={formik.handleChange}
            handleBlur={formik.handleBlur}
            touched={formik.touched}
            errors={formik.errors}
          />

          <PasswordField
            label="New Password"
            name="newPassword"
            placeholder="Password (8 or more characters)"
            values={formik.values}
            handleChange={formik.handleChange}
            handleBlur={formik.handleBlur}
            touched={formik.touched}
            errors={formik.errors}
          />

          <PasswordField
            label="Confirm Password"
            name="confirmPassword"
            placeholder="Password (8 or more characters)"
            values={formik.values}
            handleChange={formik.handleChange}
            handleBlur={formik.handleBlur}
            touched={formik.touched}
            errors={formik.errors}
          />
        </div>

        <div className="d-flex gap-3 gap-md-4 mt-5">
          <Button type="submit" disabled={formik.isSubmitting}>
            {isPending ? "Please wait..." : "Submit"}
          </Button>
          <Button
            className="bordered-btn"
            variant="secondary"
            onClick={() => formik.resetForm()}
            type="button"
          >
            Cancel
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default ChangePassword;
