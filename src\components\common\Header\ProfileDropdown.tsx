import defaultProfile from "@/assets/images/user.png";
import { useLogout } from "@/hooks";
import { ROUTE_PATH } from "@/routes";
import useUserStore from "@/stores/user";
import { calculateAgeByDOB } from "@/utils";
import { IMAGE_PATHS } from "@/utils/image-path";
import { Gallery, Logout, Setting2, User } from "iconsax-react";
import { Dropdown, Image } from "react-bootstrap";
import { Link } from "react-router-dom";

const ProfileDropdown = () => {
  const userData = useUserStore((state) => state.userInfo.user);
  const onLogout = useLogout();

  return (
    <Dropdown align="end" className="profile-dropdown-wrapper">
      <Dropdown.Toggle
        variant="light"
        className="d-flex gap-2 align-items-center user-profile"
        id="profile-dropdown"
      >
        <span className="user-profile-name">{userData.username}</span>
        <Image
          src={
            userData.avatar
              ? `${import.meta.env.VITE_S3_BASE_URL}${userData.avatar}`
              : defaultProfile
          }
          alt="profile"
          roundedCircle
          className="user-profile-img object-fit-cover"
        />
      </Dropdown.Toggle>
      <Dropdown.Menu className="profile-dropdown-menu border-0 p-0">
        <div className="d-flex align-items-center gap-2 p-3">
          <Image
            src={
              userData.avatar
                ? `${import.meta.env.VITE_S3_BASE_URL}${userData.avatar}`
                : defaultProfile
            }
            alt="profile"
            roundedCircle
            className="user-profile-img object-fit-cover"
          />
          <div className="d-flex flex-column gap-1">
            <div className="user-name">
              {userData.username},{" "}
              {calculateAgeByDOB(userData?.customer_profile?.dob)}
            </div>
            {userData?.country?.name && (
              <div className="location">
                {userData?.country?.name}
                {userData?.city ? `, ${userData.city}` : ""}
              </div>
            )}
          </div>
        </div>
        <div className="menu-display">
          <Dropdown.Item
            as={Link}
            to={ROUTE_PATH.MYPROFILE}
            className="d-flex align-items-center item"
          >
            <User size="16" color="#141414" /> View profile
          </Dropdown.Item>

          <Dropdown.Item
            as={Link}
            to={`${ROUTE_PATH.MYPROFILE}?tabKey=myPhotos`}
            className="d-flex align-items-center  item"
          >
            <Gallery size="16" color="#141414" /> My Photos
          </Dropdown.Item>
        </div>

        <Dropdown.Item
          as={Link}
          to={`${ROUTE_PATH.MYPROFILE}?tabKey=settings`}
          className="d-flex align-items-center item"
        >
          <Setting2 size="16" color="#141414" /> Settings
        </Dropdown.Item>
        <Dropdown.Item
          onClick={onLogout}
          className="d-flex align-items-center item logout-link"
        >
          <Logout size="16" color="#FF0101" /> Log out
        </Dropdown.Item>
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default ProfileDropdown;
