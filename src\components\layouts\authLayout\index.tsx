import { ROUTE_PATH } from "@/routes";
import { IMAGE_PATHS } from "@/utils/image-path";
import { Image } from "react-bootstrap";
import { Link, Outlet } from "react-router-dom";
import "./styles.scss";

const AuthLayout = () => {
  return (
    <div className="auth-layout d-flex flex-lg-row flex-column">
      <Link
        to={ROUTE_PATH.LOGIN}
        className="logo-container position-absolute top-0 start-0 p-3 z-3 filter-invert"
      >
        <Image src={IMAGE_PATHS.LOGO} alt="logo" fluid />
      </Link>
      <div className="auth-layout-left"></div>

      <div className="auth-layout-right d-flex align-items-center justify-content-center">
        <Outlet />
      </div>
    </div>
  );
};

export default AuthLayout;
