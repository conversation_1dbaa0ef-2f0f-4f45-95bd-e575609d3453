import { useResetPasswordMutation } from "@/api";
import Pass<PERSON><PERSON>ield from "@/components/common/PasswordField";
import {
  confirmPasswordValidation,
  passwordValidation,
} from "@/formSchema/schemaValidations";
import { ROUTE_PATH } from "@/routes";
import { useFormik } from "formik";
import React from "react";
import { Button, Form } from "react-bootstrap";
import toast from "react-hot-toast";
import { useNavigate, useSearchParams } from "react-router-dom";
import * as Yup from "yup";

const ResetPassword: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const email = searchParams.get("email") || undefined;

  const { mutateAsync: resetPassword, isPending } = useResetPasswordMutation();

  const formik = useFormik({
    initialValues: {
      password: "",
      confirmPassword: "",
    },
    validationSchema: Yup.object({
      password: passwordValidation("Password"),
      confirmPassword: confirmPasswordValidation("password"),
    }),
    onSubmit: async (values: any, { setSubmitting }: any) => {
      if (!email) return;
      try {
        const payload = {
          password: values.password,
          email,
        };
        const response: any = await resetPassword(payload);
        if (response?.success) {
          toast.success(response?.message);
          navigate(ROUTE_PATH.LOGIN);
        }
      } catch (error) {
        console.log(error);
      } finally {
        setSubmitting(false);
      }
    },
  });

  const { values, handleChange, handleBlur, touched, errors } = formik;

  return (
    <div className="auth-form bg-white d-flex flex-column justify-content-between">
      <div className="d-flex flex-column gap-3">
        <div className="auth-form-heading text-center">
          <h3>
            Reset <span>Password!</span>
          </h3>
        </div>
        <Form onSubmit={formik.handleSubmit}>
          <div className="form-input-group d-flex flex-column">
            <PasswordField
              label="Password"
              name="password"
              placeholder="Password (8 or more characters)"
              values={values}
              handleChange={handleChange}
              handleBlur={handleBlur}
              touched={touched}
              errors={errors}
            />

            <PasswordField
              label="Confirm Password"
              name="confirmPassword"
              placeholder="Confirm your password"
              values={values}
              handleChange={handleChange}
              handleBlur={handleBlur}
              touched={touched}
              errors={errors}
            />
          </div>
          <Button type="submit" className="w-100 mt-3 mt-sm-4">
            {isPending ? "Please wait..." : "Reset Password"}
          </Button>
        </Form>
      </div>
    </div>
  );
};

export default ResetPassword;
