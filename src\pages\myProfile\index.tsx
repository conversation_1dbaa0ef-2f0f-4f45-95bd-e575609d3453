import { useMyProfile, useUpdateProfileMutation } from "@/api/user.api";
import { useMaster } from "@/api/utils.api";
import AboutMeSection from "@/components/profileManagement/AboutMeSection";
import AppearanceSection from "@/components/profileManagement/AppearanceSection";
import BasicInfoSection from "@/components/profileManagement/BasicInfoSection";
import FamilySection from "@/components/profileManagement/FamilySection";
import LifestyleSection from "@/components/profileManagement/LifestyleSection";
import LocationSection from "@/components/profileManagement/LocationSection";
import MemoSection from "@/components/profileManagement/MemoSection";
import PersonalAttributesSection from "@/components/profileManagement/PersonalAttributesSection";
import ProfilePictureSection from "@/components/profileManagement/ProfilePictureSection";
import { useLogout } from "@/hooks";
import { useProfileAvatar } from "@/hooks/useProfileAvatar";
import useUserStore, { setUserInfo } from "@/stores/user";
import { IMAGE_PATHS } from "@/utils/image-path";
import { useFormik } from "formik";
import { AddCircle } from "iconsax-react";
import React, { useEffect, useState } from "react";
import {
  Accordion,
  Button,
  Col,
  Container,
  Form,
  Image,
  Nav,
  Row,
  Tab,
} from "react-bootstrap";
import toast from "react-hot-toast";
import defaultProfile from "../../assets/images/user.png";
import ChangePassword from "./ChangePassword";
import "./styles.scss";
import { useSearchParams } from "react-router-dom";
import Settings from "./Settings";

type tabKeyType = "viewProfile" | "myPhotos" | "changePassword" | "settings";

const MyProfile: React.FC = () => {
  const { data: myProfile } = useMyProfile();

  const userInfo = useUserStore((state) => state.userInfo);
  const { user: userData } = userInfo;
  const { mutateAsync: updateProfile } = useUpdateProfileMutation();
  const { data: master = {}, isLoading: isMasterLoading } = useMaster();
  const onLogout = useLogout();
  const [searchParams, setSearchParams] = useSearchParams();
  const defaultTabKey: tabKeyType =
    (searchParams.get("tabKey") as tabKeyType) || "viewProfile";
  const [currentKey, setCurrentKey] = useState(defaultTabKey);

  React.useEffect(() => {
    if (myProfile && Object.keys(myProfile).length > 0) {
      setUserInfo({
        ...userInfo,
        user: myProfile,
      });
    }
  }, [myProfile]);

  useEffect(() => {
    if (defaultTabKey) {
      setCurrentKey(defaultTabKey);
    }
  }, [defaultTabKey]);

  const initialAvatar = (userData as any)?.avatar || defaultProfile;
  const { preview, handleImageChange } = useProfileAvatar(initialAvatar);

  const appearanceOptions = React.useMemo(
    () =>
      (master.appearance || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.appearance]
  );
  const relationshipStatusOptions = React.useMemo(
    () =>
      (master.relationship_status || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.relationship_status]
  );
  const personalityOptions = React.useMemo(
    () =>
      (master.personality || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.personality]
  );
  const eyeColorOptions = React.useMemo(
    () =>
      (master.eye_color || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.eye_color]
  );
  const bodyTypeOptions = React.useMemo(
    () =>
      (master.body_type || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.body_type]
  );
  const hairColorOptions = React.useMemo(
    () =>
      (master.hair_color || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.hair_color]
  );
  const smokingHabitOptions = React.useMemo(
    () =>
      (master.smoking_habits || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.smoking_habits]
  );
  const drinkingHabitOptions = React.useMemo(
    () =>
      (master.drinking_habits || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.drinking_habits]
  );
  const bestFeatureOptions = React.useMemo(
    () =>
      (master.best_feature || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.best_feature]
  );
  const bodyArtOptions = React.useMemo(
    () =>
      (master.body_art || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.body_art]
  );
  const sexualOrientationOptions = React.useMemo(
    () =>
      (master.sexual_orientation || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.sexual_orientation]
  );
  const ethnicityOptions = React.useMemo(
    () =>
      (master.ethnicity || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.ethnicity]
  );
  const starSignOptions = React.useMemo(
    () =>
      (master.star_sign || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.star_sign]
  );
  const seekingForOptions = React.useMemo(
    () =>
      (master.seeking_for || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.seeking_for]
  );
  const genderOptions = React.useMemo(
    () =>
      (master.gender || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.gender]
  );
  const religionOptions = React.useMemo(
    () =>
      (master.religion || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.religion]
  );
  const interestsOptions = React.useMemo(
    () =>
      (master.interest || []).map((item: any) => ({
        value: item.id,
        label: item.title,
      })),
    [master.interest]
  );

  const initialValues = {
    name: userData?.name || undefined,
    username: userData?.username || undefined,
    email: userData?.email || undefined,
    gender: (userData as any)?.customer_profile?.gender?.id || undefined,
    seekingFor:
      (userData as any)?.customer_profile?.seekingFor?.id || undefined,
    dob: (userData as any)?.customer_profile?.dob || undefined,
    appearance:
      (userData as any)?.customer_profile?.appearance?.id || undefined,
    hairColor: (userData as any)?.customer_profile?.hairColor?.id || undefined,
    eyeColor: (userData as any)?.customer_profile?.eyeColor?.id || undefined,
    bodyType: (userData as any)?.customer_profile?.bodyType?.id || undefined,
    bestFeature:
      (userData as any)?.customer_profile?.bestFeature?.id || undefined,
    bodyArt: (userData as any)?.customer_profile?.bodyArt?.id || undefined,
    height: (userData as any)?.customer_profile?.height || undefined,
    weight: (userData as any)?.customer_profile?.weight || undefined,
    relationshipStatus:
      (userData as any)?.customer_profile?.relationshipStatus?.id || undefined,
    starSign: (userData as any)?.customer_profile?.starSign?.id || undefined,
    smokingHabit:
      (userData as any)?.customer_profile?.smokingHabit?.id || undefined,
    drinkingHabit:
      (userData as any)?.customer_profile?.drinkingHabit?.id || undefined,
    sexualOrientation:
      (userData as any)?.customer_profile?.sexualOrientation?.id || undefined,
    personality:
      (userData as any)?.customer_profile?.personality?.id || undefined,
    ethnicity: (userData as any)?.customer_profile?.ethnicity?.id || undefined,
    religion: (userData as any)?.customer_profile?.religion?.id || undefined,
    interest: (userData as any)?.customer_profile?.interest?.id || undefined,
    city: (userData as any)?.city || undefined,
    kids: (userData as any)?.customer_profile?.kids || undefined,
    aboutMe: (userData as any)?.customer_profile?.aboutMe || undefined,
    avatar: (userData as any)?.avatar || undefined,
  };

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validateOnChange: false,
    validateOnBlur: true,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const payload = { ...values, avatar: preview };
        const result: any = await updateProfile(payload);
        if (result?.success) {
          toast.success(result?.message || "Profile updated successfully!");
        }
      } finally {
        setSubmitting(false);
      }
    },
  });

  const handleSelectChange = React.useCallback(
    (field: string) => (option: any) => {
      formik.setFieldValue(field, option?.value || "");
    },
    [formik]
  );

  const handleBlurUncontrolled = React.useCallback(
    (field: string) =>
      (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        formik.setFieldValue(
          field,
          e.target.type === "number" ? Number(e.target.value) : e.target.value
        );
      },
    [formik]
  );

  const handleSelect = (value: any) => {
    setCurrentKey(value);
    setSearchParams({ tabKey: value });
  };

  return (
    <div className="my-profile">
      <Container fluid>
        <h3 className="fw-bold mb-4">My Profile</h3>
        <Tab.Container
          defaultActiveKey={defaultTabKey}
          activeKey={currentKey}
          onSelect={handleSelect}
        >
          <Row>
            {/* Sidebar */}
            <Col xxl={3} lg={4} md={5}>
              <div className="my-profile-menus">
                <ProfilePictureSection
                  values={{ ...formik.values, avatar: preview }}
                  setFieldValue={formik.setFieldValue}
                  preview={preview}
                  handleImageChange={handleImageChange}
                />
                <Nav variant="pills" className="flex-column">
                  <Nav.Item>
                    <Nav.Link eventKey="viewProfile">View Profile</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="myPhotos">My Photos</Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="changePassword">
                      Change Password
                    </Nav.Link>
                  </Nav.Item>
                  <Nav.Item>
                    <Nav.Link eventKey="settings">Settings</Nav.Link>
                  </Nav.Item>
                  <Nav.Item className="logout-link" onClick={onLogout}>
                    <Nav.Link eventKey="logout">Logout</Nav.Link>
                  </Nav.Item>
                </Nav>
              </div>
            </Col>
            {/* Content Area */}
            <Col xxl={9} lg={8} md={7}>
              <Tab.Content className="my-profile-content h-100">
                <Tab.Pane eventKey="viewProfile">
                  <Form noValidate onSubmit={formik.handleSubmit}>
                    <Accordion
                      className="d-flex flex-column"
                      defaultActiveKey={["0"]}
                    >
                      <MemoSection>
                        <BasicInfoSection
                          formik={formik}
                          handleBlurUncontrolled={handleBlurUncontrolled}
                          seekingForOptions={seekingForOptions}
                          genderOptions={genderOptions}
                        />
                      </MemoSection>
                      <MemoSection>
                        <AppearanceSection
                          formik={formik}
                          handleBlurUncontrolled={handleBlurUncontrolled}
                          handleSelectChange={handleSelectChange}
                          appearanceOptions={appearanceOptions}
                          hairColorOptions={hairColorOptions}
                          eyeColorOptions={eyeColorOptions}
                          bodyTypeOptions={bodyTypeOptions}
                          bestFeatureOptions={bestFeatureOptions}
                          bodyArtOptions={bodyArtOptions}
                          isMasterLoading={isMasterLoading}
                        />
                      </MemoSection>
                      <MemoSection>
                        <LifestyleSection
                          formik={formik}
                          handleSelectChange={handleSelectChange}
                          relationshipStatusOptions={relationshipStatusOptions}
                          starSignOptions={starSignOptions}
                          smokingHabitOptions={smokingHabitOptions}
                          drinkingHabitOptions={drinkingHabitOptions}
                          sexualOrientationOptions={sexualOrientationOptions}
                          isMasterLoading={isMasterLoading}
                        />
                      </MemoSection>
                      <MemoSection>
                        <PersonalAttributesSection
                          formik={formik}
                          handleSelectChange={handleSelectChange}
                          personalityOptions={personalityOptions}
                          ethnicityOptions={ethnicityOptions}
                          religionOptions={religionOptions}
                          interestsOptions={interestsOptions}
                          isMasterLoading={isMasterLoading}
                        />
                      </MemoSection>
                      <MemoSection>
                        <LocationSection
                          formik={formik}
                          handleBlurUncontrolled={handleBlurUncontrolled}
                        />
                      </MemoSection>
                      <MemoSection>
                        <FamilySection
                          formik={formik}
                          handleBlurUncontrolled={handleBlurUncontrolled}
                        />
                      </MemoSection>
                      <MemoSection>
                        <AboutMeSection
                          formik={formik}
                          handleBlurUncontrolled={handleBlurUncontrolled}
                        />
                      </MemoSection>
                    </Accordion>
                    <div className="d-flex gap-3 gap-md-4 mt-4 mt-md-5">
                      <Button type="submit" disabled={formik.isSubmitting}>
                        Submit
                      </Button>
                      <Button className="bordered-btn" type="button">
                        Cancel
                      </Button>
                    </div>
                  </Form>
                </Tab.Pane>
                <Tab.Pane eventKey="myPhotos">
                  <div className="my-photos d-flex flex-wrap">
                    <div className="my-photos-upload position-relative d-flex align-items-center justify-content-center">
                      <div className="text-center">
                        <AddCircle size="55" color="#141414" variant="Bold" />
                        <h6 className="mb-0 mt-3">Upload Photos</h6>
                      </div>
                      <Form.Control
                        type="file"
                        accept="image/jpeg,image/jpg,image/png"
                        className="photo-upload"
                      />
                    </div>
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList1}
                      alt="user-pic"
                    />
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList2}
                      alt="user-pic"
                    />
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList3}
                      alt="user-pic"
                    />
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList4}
                      alt="user-pic"
                    />
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList5}
                      alt="user-pic"
                    />
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList6}
                      alt="user-pic"
                    />
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList7}
                      alt="user-pic"
                    />
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList8}
                      alt="user-pic"
                    />
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList9}
                      alt="user-pic"
                    />
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList1}
                      alt="user-pic"
                    />
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList4}
                      alt="user-pic"
                    />
                    <Image
                      className="my-photos-img"
                      src={IMAGE_PATHS.memberList7}
                      alt="user-pic"
                    />
                  </div>
                </Tab.Pane>
                <Tab.Pane eventKey="changePassword">
                  <ChangePassword />
                </Tab.Pane>
                <Tab.Pane eventKey="settings">
                  <Settings />
                </Tab.Pane>
                <Tab.Pane eventKey="logout">
                  <h2>Logout</h2>
                  <p>You are now logged out.</p>
                </Tab.Pane>
              </Tab.Content>
            </Col>
          </Row>
        </Tab.Container>
      </Container>
    </div>
  );
};

export default MyProfile;
