import { create } from 'zustand';
import io from 'socket.io-client';

const useChatStore = create((set) => ({
  activeChatUser: null,
  messages: {},
  socket: null,

  setActiveChatUser: (user) => set({ activeChatUser: user }),

  sendMessage: (toUserId, message) => {
    set((state) => {
      const socket = state.socket;
      if (socket) {
        socket.emit('sendMessage', { toUserId, message });
        return {
          messages: {
            ...state.messages,
            [toUserId]: [...(state.messages[toUserId] || []), { sender: 'me', text: message }],
          },
        };
      }
      return state;
    });
  },

  receiveMessage: (fromUserId, message) => {
    set((state) => ({
      messages: {
        ...state.messages,
        [fromUserId]: [...(state.messages[fromUserId] || []), { sender: 'other', text: message }],
      },
    }));
  },

  initializeSocket: () => {
    const socket = io('http://localhost:3001'); // Replace with your WebSocket server URL
    set({ socket });

    socket.on('receiveMessage', ({ fromUserId, message }) => {
      useChatStore.getState().receiveMessage(fromUserId, message);
    });

    return () => socket.disconnect();
  },
}));

// Initialize socket when the store is created
// useChatStore.getState().initializeSocket();

export default useChatStore;