import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "./apiClient";
import { API_ENDPOINTS } from "@/globals";
import axios from "axios";

export const useCountries = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.COUNTRIES);
      return response?.data?.country || [];
    },
    queryKey: ["countries"],
  });

export const useMaster = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.MASTER);
      return response?.data?.master || {};
    },
    queryKey: ["master"],
  });

export const useGetS3PresignedUrlMutation = () =>
  useMutation({
    mutationFn: async (params: any) => {
      const response = await apiClient.get(
        API_ENDPOINTS.GET_S3_PRE_SIGNED_URL,
        { params },
      );
      return response;
    },
  });

export const useUploadFileToS3Mutation = () =>
  useMutation({
    mutationFn: async ({ url, file }: any) => {
      const response = await axios.put(url, file, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      return response;
    },
  });