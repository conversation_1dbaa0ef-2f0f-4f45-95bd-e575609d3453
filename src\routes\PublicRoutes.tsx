import { Login, Signup } from "@/pages";
import ForgotPassword from "@/pages/auth/forgotPassword";
import ResetPassword from "@/pages/auth/resetPassword";
import VerifyAccount from "@/pages/auth/verifyAccount";
import { Route } from "react-router-dom";
import { ROUTE_PATH } from "./routePath";
import AuthLayout from "@/components/layouts/authLayout";

const PublicRoutes = () => {
  return (
    <Route element={<AuthLayout />}>
      <Route path={ROUTE_PATH.LOGIN} element={<Login />} />
      <Route path={ROUTE_PATH.SIGNUP} element={<Signup />} />
      <Route path={ROUTE_PATH.FORGOTPASSWORD} element={<ForgotPassword />} />
      <Route path={ROUTE_PATH.RESETPASSWORD} element={<ResetPassword />} />
      <Route path={ROUTE_PATH.VERIFYACCOUNT} element={<VerifyAccount />} />
    </Route>
  );
};

export default PublicRoutes;
