@use "@/variables" as *;

.notfound-form {
  background: transparent;
  max-width: 480px;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  min-height: 320px;
  margin: 60px auto 0 auto;

  .notfound-icon {
    width: 72px;
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    img,
    .img-fluid,
    .image {
      width: 100%;
      height: auto;
      max-width: 72px;
      max-height: 72px;
      object-fit: contain;
      display: block;
    }
  }

  .auth-form-heading {
    font-size: 32px;
    font-weight: 800;
    color: $primary-color;
    margin-bottom: 0.5rem;
  }

  .auth-form-description {
    font-size: 16px;
    color: $text-color;
    margin-bottom: 0;
  }

  .submit-btn {
    color: $primary-color;
    font-weight: 700;
    border-radius: 12px;
    padding: 12px 0;
    background: transparent;
    transition:
      background 0.2s,
      color 0.2s;
    font-size: 16px;
    letter-spacing: 1px;
    &:hover {
      background: $primary-color;
      color: $white-color;
      text-decoration: none;
    }
  }
}

@media (max-width: 575px) {
  .notfound-form {
    margin: 30px auto 0 auto;
    .notfound-icon {
      width: 48px;
      height: 48px;
      img,
      .img-fluid,
      .image {
        max-width: 48px;
        max-height: 48px;
      }
    }
    .auth-form-heading {
      font-size: 20px;
    }
    .auth-form-description {
      font-size: 13px;
    }
    .submit-btn {
      font-size: 14px;
      padding: 10px 0;
    }
  }
}
