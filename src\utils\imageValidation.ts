// Utility for image file validation

export const ACCEPTED_IMAGE_TYPES = [
  'image/png',
  'image/jpeg',
  'image/jpg',
];

export const ACCEPTED_IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg'];

export const MAX_IMAGE_SIZE_MB = 20;
export const MAX_IMAGE_SIZE_BYTES = MAX_IMAGE_SIZE_MB * 1024 * 1024;

export function validateImageFile(file: File): string | null {
  if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
    return 'Only PNG and JPG images are allowed.';
  }
  if (file.size > MAX_IMAGE_SIZE_BYTES) {
    return `Image size must be less than ${MAX_IMAGE_SIZE_MB}MB.`;
  }
  return null;
} 