import { SignupInterface } from "@/types/auth";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface AuthState {
  signupUserInfo: SignupInterface | null;
  setSignupUserInfo: (data: SignupInterface) => void;
  clearSignupUserInfo: () => void;
}

const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set) => ({
        signupUserInfo: null,
        setSignupUserInfo: (data) => set({ signupUserInfo: data }),
        clearSignupUserInfo: () => set({ signupUserInfo: null }),
      }),
      {
        name: "signupUserInfo",
        storage: {
          getItem: (name) => {
            const item = sessionStorage.getItem(name);
            return item ? JSON.parse(item) : null;
          },
          setItem: (name, value) => {
            sessionStorage.setItem(name, JSON.stringify(value));
          },
          removeItem: (name) => sessionStorage.removeItem(name),
        },
      }
    )
  )
);

export default useAuthStore; 