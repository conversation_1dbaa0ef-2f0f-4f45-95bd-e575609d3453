import { useLoginMutation } from "@/api/auth.api";
import Pass<PERSON><PERSON>ield from "@/components/common/PasswordField";
import {
  emailValidation,
  stringRequiredValidation,
} from "@/formSchema/schemaValidations";
import { ROUTE_PATH } from "@/routes";
import useUserStore from "@/stores/user";
import { LoginInterface } from "@/types/auth";
import { useFormik } from "formik";
import React from "react";
import { Button, Form } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import * as Yup from "yup";

const Login: React.FC = () => {
  const navigate = useNavigate();
  const { setUserInfo, setRememberMeInfo } = useUserStore();
  const { mutateAsync: login, isPending } = useLoginMutation();
  const rememberMeEmail = useUserStore(
    (state) => state.rememberMeInfo?.email || ""
  );

  const formik = useFormik<LoginInterface & { rememberMe: boolean }>({
    initialValues: {
      email: rememberMeEmail,
      password: "",
      rememberMe: !!rememberMeEmail,
    },
    validationSchema: Yup.object({
      email: emailValidation,
      password: stringRequiredValidation("Password"),
      rememberMe: Yup.boolean(),
    }),
    onSubmit: async (values, { setSubmitting }) => {
      try {
        const response = await login({
          email: values.email,
          password: values.password,
          role: "customer",
        });
        if (response?.data?.access_token && response?.data?.user?.id) {
          setUserInfo(response.data);
          if (values.rememberMe) {
            setRememberMeInfo({ email: values.email });
          } else {
            setRememberMeInfo({ email: "" });
          }
          navigate(ROUTE_PATH.HOME);
        }
      } catch (err: unknown) {
        console.log(err);
      } finally {
        setSubmitting(false);
      }
    },
  });

  const {
    handleChange,
    handleBlur,
    handleSubmit,
    values,
    touched,
    errors,
    isSubmitting,
    submitCount,
  } = formik;

  return (
    <div className="auth-form bg-white d-flex flex-column justify-content-between">
      <div className="d-flex flex-column gap-4">
        <div className="auth-form-heading text-center">
          <h3>
            Welcome to <span>Redsoft!</span>
          </h3>
        </div>
        <Form noValidate onSubmit={handleSubmit}>
          <div className="form-input-group d-flex flex-column">
            <Form.Group className="form-input" controlId="email">
              <Form.Label>Email</Form.Label>
              <Form.Control
                type="email"
                name="email"
                placeholder="Please enter email"
                value={values.email}
                onChange={handleChange}
                onBlur={handleBlur}
                isInvalid={!!touched.email && !!errors.email}
              />
              <Form.Control.Feedback type="invalid">
                {errors.email}
              </Form.Control.Feedback>
            </Form.Group>
            <PasswordField
              label="Password"
              name="password"
              placeholder="Enter password"
              values={values}
              handleChange={handleChange}
              handleBlur={handleBlur}
              touched={touched}
              errors={errors}
              submitCount={submitCount}
            />
          </div>
          <div className="d-flex align-items-center justify-content-between text-end mt-2 mt-sm-3">
            <Form.Check type="checkbox" id={`check-api-checkbox`}>
              <Form.Check.Input
                type="checkbox"
                name="rememberMe"
                checked={values.rememberMe}
                onChange={handleChange}
                onBlur={handleBlur}
              />
              <Form.Check.Label>Remember Me</Form.Check.Label>
            </Form.Check>
            <Link to={ROUTE_PATH.FORGOTPASSWORD} className="forgot-link">
              Forgot Password?
            </Link>
          </div>
          <Button
            type="submit"
            className="w-100 mt-3 mt-sm-4"
            disabled={isSubmitting || isPending}
          >
            {isPending ? "Logging in..." : "Login"}
          </Button>
        </Form>
      </div>
      <div className="text-center mt-3 mt-sm-4">
        <p className="mb-0">
          Don't have an account? <Link to={ROUTE_PATH.SIGNUP}>Sign up</Link>
        </p>
      </div>
    </div>
  );
};

export default Login;
