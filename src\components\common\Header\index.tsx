import { IMAGE_PATHS } from "@/utils/image-path";
import React, { useState } from "react";
import {
  Badge,
  Button,
  Container,
  Dropdown,
  Image,
  Nav,
  Navbar,
  ProgressBar,
} from "react-bootstrap";
import { Link, Outlet, useLocation } from "react-router-dom";
import ProfileDropdown from "./ProfileDropdown";
import "./styles.scss";
import { ROUTE_PATH } from "@/routes";

const notifications = [
  {
    user: "GoodGirl__24",
    avatar: IMAGE_PATHS.NotificationUser1,
    time: "Yesterday at 11:42 PM",
    read: false,
  },
  {
    user: "GoodGirl__24",
    avatar: IMAGE_PATHS.NotificationUser2,
    time: "Yesterday at 11:42 PM",
    read: true,
  },
  {
    user: "GoodGirl__24",
    avatar: IMAGE_PATHS.NotificationUser3,
    time: "Yesterday at 11:42 PM",
    read: true,
  },
  {
    user: "GoodGirl__24",
    avatar: IMAGE_PATHS.NotificationUser1,
    time: "Yesterday at 11:42 PM",
    read: true,
  },
  {
    user: "GoodGirl__24",
    avatar: IMAGE_PATHS.NotificationUser2,
    time: "Yesterday at 11:42 PM",
    read: true,
  },
];
const Header: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(true);
  const location = useLocation();

  const { pathname } = location;

  const isActive = (path: string) => pathname === path;

  return (
    <>
      <header className={`header  ${isAuthenticated ? "loggedIn" : "not"}`}>
        <Navbar expand="lg" sticky="top" className="p-0">
          <Container
            fluid
            className="d-flex justify-content-between align-items-center"
          >
            <Navbar.Brand as={Link} to={ROUTE_PATH.HOME} className="main-logo">
              <Image src={IMAGE_PATHS.LOGO} alt="logo" />
            </Navbar.Brand>
            <Navbar.Toggle aria-controls="responsive-navbar-nav" />

            <Navbar.Collapse id="responsive-navbar-nav">
              <Nav className="mx-auto">
                <Nav.Link
                  as={Link}
                  to={ROUTE_PATH.HOME}
                  className={
                    isActive(ROUTE_PATH.HOME) ? "active text-purple" : ""
                  }
                >
                  Members
                </Nav.Link>
                <Nav.Link
                  as={Link}
                  to={ROUTE_PATH.MESSAGES}
                  className={
                    isActive(ROUTE_PATH.MESSAGES) ? "active text-purple" : ""
                  }
                >
                  Messages
                </Nav.Link>
                <Nav.Link
                  as={Link}
                  to={ROUTE_PATH.FAVORITES}
                  className={
                    isActive(ROUTE_PATH.FAVORITES) ? "active text-purple" : ""
                  }
                >
                  Favorites
                </Nav.Link>
                <Nav.Link
                  as={Link}
                  to={ROUTE_PATH.VIEWEDME}
                  className={
                    isActive(ROUTE_PATH.VIEWEDME) ? "active text-purple" : ""
                  }
                >
                  Who Viewed Me
                </Nav.Link>
                <Nav.Link
                  as={Link}
                  to={ROUTE_PATH.FLIRTS}
                  className={
                    isActive(ROUTE_PATH.FLIRTS) ? "active text-purple" : ""
                  }
                >
                  Flirts
                </Nav.Link>
              </Nav>
            </Navbar.Collapse>

            <div className="navbar-right-menu d-flex align-items-center gap-3">
              <Dropdown align="end" className="coin-dropdown-wrapper">
                <Dropdown.Toggle
                  variant="light"
                  className="d-flex align-items-center gap-2 bg-white rounded-pill px-3 py-2"
                >
                  <Image src={IMAGE_PATHS.Coins} alt="" />
                  <span className="coins-number">200</span>
                </Dropdown.Toggle>

                <Dropdown.Menu className="coin-details-dropdown p-0 border-0">
                  <div className="p-3 coin-details-dropdown-header">
                    <h6 className="title mb-0">Coin Details</h6>
                  </div>
                  <div className="d-flex flex-column p-3">
                    <div className="d-flex justify-content-between align-items-center balance-details">
                      <div className="d-flex flex-column gap-1 ">
                        <div className="plan d-flex align-items-center gap-2">
                          Basic Plan{" "}
                          <Badge bg="success" pill>
                            Active
                          </Badge>
                        </div>
                        <small className="balance">Account Balance</small>
                      </div>
                      <div className="total-coins">
                        800 Coins <sub className="small-text">per month</sub>
                      </div>
                    </div>
                    <div className="d-flex flex-column gap-2">
                      <div className="remaining-coins">
                        200 coins <span>remaining</span>
                      </div>
                      <ProgressBar now={25} className="mb-3 custom-progress" />
                    </div>
                    <Link to={ROUTE_PATH.COINPURCHASE}>
                      <Button
                        variant="gradient-purple"
                        className="d-flex justify-content-center align-items-center gap-2 plan-btn"
                      >
                        Upgrade Plan
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                        >
                          <path
                            d="M17.6577 16.2427V6.34322M17.6577 6.34322H7.75818M17.6577 6.34322L6.34397 17.6569"
                            stroke="white"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                      </Button>
                    </Link>
                  </div>
                </Dropdown.Menu>
              </Dropdown>
              <Dropdown align="end" className="notification-dropdown-wrapper">
                <Dropdown.Toggle
                  variant="light"
                  className="rounded-circle p-2 d-flex align-items-center justify-content-center notification-icon"
                  id="notification-dropdown"
                >
                  <Image src={IMAGE_PATHS.NotificationIcon} alt="" />
                  <span className="dot"></span>
                </Dropdown.Toggle>
                <Dropdown.Menu className="notification-dropdown border-0 p-0">
                  <div className="d-flex justify-content-between align-items-center p-3">
                    <h6 className="mb-0 notifications-title">Notifications</h6>
                    <Button className="mark-read-btn d-flex align-items-center gap-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M12.9111 5.77899C13.0359 5.88797 13.1124 6.04207 13.1236 6.2074C13.1349 6.37274 13.08 6.53577 12.9711 6.66066L6.42356 14.1607C6.3649 14.2279 6.29253 14.2817 6.21132 14.3186C6.1301 14.3555 6.04193 14.3746 5.95273 14.3746C5.86352 14.3746 5.77535 14.3555 5.69414 14.3186C5.61293 14.2817 5.54056 14.2279 5.4819 14.1607L2.86273 11.1607C2.75767 11.0353 2.70595 10.8737 2.7187 10.7106C2.73144 10.5475 2.80762 10.3959 2.93088 10.2883C3.05415 10.1808 3.21467 10.1258 3.37798 10.1353C3.5413 10.1448 3.69439 10.2179 3.8044 10.339L5.95273 12.7998L12.0294 5.83899C12.1384 5.71415 12.2925 5.63769 12.4578 5.62644C12.6231 5.61519 12.7862 5.67006 12.9111 5.77899ZM17.0986 5.84982C17.3486 6.08732 17.3577 6.48316 17.1194 6.73316L9.9769 14.2332C9.91451 14.2987 9.83865 14.3499 9.75454 14.3832C9.67044 14.4165 9.58011 14.4312 9.48978 14.4262C9.39945 14.4212 9.31128 14.3967 9.23136 14.3543C9.15144 14.3119 9.08168 14.2527 9.0269 14.1807L8.6694 13.7115C8.57575 13.5894 8.53042 13.4371 8.5421 13.2837C8.55377 13.1302 8.62163 12.9865 8.73267 12.88C8.84372 12.7735 8.99014 12.7117 9.14391 12.7065C9.29769 12.7012 9.448 12.7528 9.56606 12.8515L16.2144 5.87066C16.3287 5.7507 16.4859 5.68103 16.6516 5.67696C16.8172 5.6729 16.9777 5.73478 17.0977 5.84899"
                          fill="#B936AD"
                        />
                      </svg>
                      Mark all as read
                    </Button>
                  </div>
                  <div className="notification-list">
                    {notifications.map((n, index) => (
                      <div
                        key={index}
                        className={`d-flex gap-2 align-items-center p-3  ${!n.read ? "bg-unread" : ""}`}
                      >
                        <Image
                          src={n.avatar}
                          roundedCircle
                          className="w-100 user-img"
                        />
                        <div className="d-flex flex-column gap-1">
                          <div className="user-name">
                            {n.user} <span>viewed your profile.</span>
                          </div>
                          <div className="time">{n.time}</div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="view-all p-3 text-center">
                    <a href="#" className="text-purple fw-semibold small">
                      View all notifications
                    </a>
                  </div>
                </Dropdown.Menu>
              </Dropdown>
              <ProfileDropdown />
            </div>
          </Container>
        </Navbar>
      </header>

      <Outlet />
    </>
  );
};

export default Header;
