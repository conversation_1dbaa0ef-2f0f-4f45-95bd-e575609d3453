import { useUpdateNotificationsMutation } from "@/api";
import useUserS<PERSON>, { setUserDetails, setUserInfo } from "@/stores/user";
import { Button, Form } from "react-bootstrap";
import toast from "react-hot-toast";
import React from "react";

const notificationFields = [
  { key: "newChatMessages", label: "New Chat Messages" },
  { key: "receivedFlirt", label: "Received Flirt" },
  { key: "receivedProfileView", label: "Received Profile View" },
  { key: "addedAsFavorite", label: "Added as Favorite" },
  { key: "newsletterView", label: "Newsletter View" },
  { key: "newsletter", label: "Newsletter" },
  {
    key: "updateMembershipInformation",
    label: "Update Membership Information",
  },
];

const Settings = () => {
  const userData = useUserStore((state) => state.userInfo.user);
  const { mutateAsync: updateNotification } = useUpdateNotificationsMutation();
  const [toggles, setToggles] = React.useState(() => {
    const initial: Record<string, boolean> = {};
    notificationFields.forEach(({ key }) => {
      initial[key] = userData?.[key] ?? false;
    });
    return initial;
  });

  const handleToggle = (key: string) => {
    setToggles((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const result = await updateNotification(toggles);
      if ((result as any)?.success) {
        toast.success(
          (result as any)?.message || "Notification settings updated!"
        );
        setUserDetails({
          ...userData,
          ...toggles,
        });
      }
    } catch (err: any) {
      toast.error(err?.message || "Failed to update notification settings");
    }
  };

  return (
    <>
      <div className="content-heading">
        <h4 className="fw-bold">Edit Notifications</h4>
        <p className="mb-0">
          Donec vitae mi vulputate, suscipit urna in, malesuada nisl.
          Pellentesque laoreet pretium nisl, et pulvinar massa eleifend sed.
          Curabitur maximus mollis.
        </p>
      </div>
      <Form onSubmit={handleSubmit}>
        <div>
          {notificationFields.map(({ key, label }) => (
            <div
              className="notification-box d-flex align-items-center justify-content-between"
              key={key}
            >
              <div>
                <h6 className="mb-1 fw-medium">{label}</h6>
                <p className="mb-0">
                  Only you and verified trex users can see pricing
                </p>
              </div>
              <Form.Check
                type="switch"
                id={`custom-switch`}
                checked={toggles[key]}
                onChange={() => handleToggle(key)}
              />
            </div>
          ))}
        </div>
        <div className="d-flex gap-3 gap-md-4 mt-5">
          <Button type="submit">Submit</Button>
          <Button className="bordered-btn" type="button">
            Cancel
          </Button>
        </div>
      </Form>
    </>
  );
};

export default Settings;
