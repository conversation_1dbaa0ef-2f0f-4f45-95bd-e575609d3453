export interface LoginInterface {
  email: string;
  password: string;
  role?: string;
}

export interface SignupInterface {
  username: string;
  email: string;
  password: string;
  seekingFor: number | undefined;
  dob: string;
  countryId: number | undefined;
  city: string;
  gender: number | undefined;
}


export interface ForgotInterface {
  type: string;
  email: string;
}

export interface ResponseInteface {
  message: string;
  success: boolean;
  data: unknown;
}

export interface ResetPasswordInterface {
  password: string;
  email: string;
}

export interface ChangePasswordInterface {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}
